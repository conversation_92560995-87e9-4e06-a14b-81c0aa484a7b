{"experiment_id": "experiment_20250706_201117", "start_time": "2025-07-06T20:11:17.554813", "config": {"target_agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"], "max_coalitions": 20, "config": {"start_date": "2025-01-06", "end_date": "2025-01-06", "stocks": ["AAPL"], "starting_cash": 1000000, "risk_free_rate": 0.02, "simulation_days": 1, "fail_on_large_gaps": false, "fill_date_gaps": true, "max_coalitions_to_simulate": null, "enable_parallel_simulation": false, "verbose": true, "enable_concurrent_execution": true, "enhanced_shapley_oprp": {"enabled": true, "cycle_length_days": 7, "baseline_operation_days": 7, "ab_testing_days": 7, "underperforming_threshold": 0.3, "statistical_significance_level": 0.05, "minimum_improvement_threshold": 0.02, "max_concurrent_experiments": 4, "auto_prompt_selection": true, "backup_original_prompts": true, "continuous_optimization": true, "weekly_shapley_calculation": true}, "use_only_winning_data": true, "min_data_points_per_agent": 5, "data_quality_threshold": 0.8, "enable_historical_tracking": true, "max_historical_weeks": 12, "opro_system": {"version": "1.0.0", "description": "OPRO优化系统配置文件", "created_at": "2025-01-03T00:00:00Z"}, "optimization": {"optimization_frequency": "weekly", "candidates_per_generation": 8, "historical_weeks_to_consider": 10, "temperature": 1.0, "max_optimization_iterations": 50, "convergence_threshold": 0.001, "prompt_length_limit": 500, "evaluation_timeout": 300, "min_improvement_threshold": 0.01, "auto_optimize_after_evaluation": true, "rollback_on_degradation": true}, "evaluation": {"enable_cache": true, "cache_ttl": 3600, "parallel_evaluation": true, "max_workers": 4, "quick_test_days": 3, "full_evaluation_trigger_threshold": 0.05}, "ab_testing": {"enable_ab_testing": false, "default_test_duration_hours": 24, "min_sample_size_per_variant": 10, "significance_level": 0.05, "max_concurrent_tests": 2}, "storage": {"results_base_path": "results/periodic_shapley", "opro_db_path": "results/opro_optimization.db", "export_directory": "results/opro_export", "backup_enabled": true, "backup_frequency": "daily", "data_retention_days": 90, "comprehensive_storage": {"enabled": true, "base_path": "data", "trading_data_path": "data/trading", "prompts_data_path": "data/prompts", "visualizations_path": "data/visualizations", "exports_path": "data/exports", "backups_path": "data/backups", "database_path": "data/comprehensive_storage.db", "auto_backup_interval_hours": 24, "data_validation_enabled": true, "compression_enabled": true, "max_file_size_mb": 100}}, "agents": {"default_agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"], "core_analyst_agents": ["NAA", "TAA", "FAA"], "outlook_agents": ["BOA", "BeOA", "NOA"], "trader_agent": "TRA", "max_prompt_history_per_agent": 50}, "llm_integration": {"primary_provider": "zhipuai", "fallback_provider": null, "model_config": {"zhipuai": {"model": "glm-4-flash", "temperature": 1.0, "max_tokens": 1000, "timeout": 30}, "openai": {"model": "gpt-3.5-turbo", "temperature": 1.0, "max_tokens": 1000, "timeout": 30}}, "rate_limiting": {"requests_per_minute": 60, "concurrent_requests": 10}}, "monitoring": {"enable_performance_tracking": true, "log_level": "INFO", "metrics_collection": true, "alert_thresholds": {"optimization_failure_rate": 0.3, "performance_degradation_threshold": -0.1, "system_error_rate": 0.05}, "dashboard_refresh_interval": 300}, "security": {"enable_prompt_validation": true, "max_prompt_length": 1000, "forbidden_patterns": ["ignore previous instructions", "system prompt", "override safety"], "enable_audit_logging": true}, "experimental": {"enable_multi_objective_optimization": false, "enable_cross_agent_collaboration": false, "enable_dynamic_coalition_optimization": false, "enable_reinforcement_learning": false}, "iterative_shapley": {"use_only_winning_data": true, "min_data_points_per_agent": 5, "data_quality_threshold": 0.8, "enable_historical_tracking": true, "max_historical_weeks": 12}}, "start_time": 1751803877.5548136}, "status": "started", "coalitions": {}, "summary": {}}