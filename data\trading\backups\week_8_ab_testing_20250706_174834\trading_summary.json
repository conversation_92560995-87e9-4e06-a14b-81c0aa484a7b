{"week_number": 8, "week_type": "ab_testing", "completion_timestamp": "2025-07-06T17:48:34.814523", "summary_data": {"week_info": {"week_id": "week_8_ab_testing_20250706_174834", "week_number": 8, "week_type": "ab_testing", "start_date": "2025-03-11", "end_date": "2025-03-19", "target_agents": ["TRA", "NSA", "FSA"], "started_at": "2025-07-06T17:48:34.804519"}, "weekly_summary": {"status": "ab_testing_complete", "week_number": 10, "phase": "ab_testing", "ab_test_data": {"week_number": 10, "test_agents": ["NSA"], "original_performance": {}, "optimized_performance": {}, "data_collected": true}, "performance_comparison": {"ab_test_week_10": {"config": {"optimized_agents": ["NSA"]}, "statistical_comparison": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "prompt_selection": {"ab_test_week_10": {"config": {"optimized_agents": ["NSA"]}, "agent_selections": {"NSA": {"selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "statistical_data": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "overall_recommendation": "optimized"}}, "update_result": {"updated_agents": [{"agent_id": "NSA", "selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "experiment_id": "ab_test_week_10", "update_timestamp": "2025-07-06T17:48:34.809524"}], "failed_updates": [], "total_agents": 1}, "weekly_summary": {"week_number": 10, "cycle_type": "ab_testing", "target_agents": ["NSA"], "performance_improvement": 0, "winning_prompts": {}, "winning_experiment_data": {"ab_test_data": {"week_number": 10, "test_agents": ["NSA"], "original_performance": {}, "optimized_performance": {}, "data_collected": true}, "performance_comparison": {"ab_test_week_10": {"config": {"optimized_agents": ["NSA"]}, "statistical_comparison": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "prompt_selection_result": {"ab_test_week_10": {"config": {"optimized_agents": ["NSA"]}, "agent_selections": {"NSA": {"selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "statistical_data": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "overall_recommendation": "optimized"}}, "target_agents": ["NSA"], "week_number": 10}, "timestamp": "2025-07-06T17:48:34.810523"}, "winning_experiment_data": {}, "next_phase": "baseline_operation"}, "integration_completed_at": "2025-07-06T17:48:34.814523"}, "data_statistics": {"total_agent_decisions": 0, "decision_type_distribution": {"buy": 0, "sell": 0, "hold": 0}, "agents_with_data": [], "performance_metrics_recorded": false, "ab_test_tracks": ["original", "optimized"]}, "files_created": ["week_config.json", "optimized_track\\performance\\performance_metrics_optimized.json"], "status": "completed"}