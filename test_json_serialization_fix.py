#!/usr/bin/env python3
"""
测试AgentInteractionLogger的JSON序列化修复
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加data目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data'))

from agent_interaction_logger import AgentInteractionLogger, safe_serialize_data

def test_json_serialization():
    """测试JSON序列化功能"""
    print("🧪 测试JSON序列化修复...")
    
    # 创建测试数据，包含各种可能导致序列化问题的类型
    test_data = {
        "timestamp": pd.Timestamp("2025-01-06"),
        "date_str": "2025-01-06",
        "price_data": pd.Series([100.0, 101.0, 102.0]),
        "numpy_int": np.int64(42),
        "numpy_float": np.float64(3.14),
        "numpy_array": np.array([1, 2, 3]),
        "regular_dict": {"key": "value"},
        "regular_list": [1, 2, 3],
        "nested_data": {
            "inner_timestamp": pd.Timestamp("2025-01-07"),
            "inner_series": pd.Series([200.0, 201.0]),
            "inner_list": [
                {"timestamp": pd.Timestamp("2025-01-08"), "value": np.float64(99.9)}
            ]
        }
    }
    
    print("📊 原始测试数据类型:")
    for key, value in test_data.items():
        print(f"  - {key}: {type(value)}")
    
    # 测试安全序列化
    try:
        safe_data = safe_serialize_data(test_data)
        print("✅ 安全序列化成功")
        
        print("📊 序列化后数据类型:")
        for key, value in safe_data.items():
            print(f"  - {key}: {type(value)} = {value}")
            
    except Exception as e:
        print(f"❌ 安全序列化失败: {e}")
        return False
    
    return True

def test_agent_logger_with_problematic_data():
    """测试AgentInteractionLogger处理问题数据"""
    print("\n🧪 测试AgentInteractionLogger处理问题数据...")
    
    # 创建日志记录器
    logger = AgentInteractionLogger(
        base_path="data/testing",
        enabled=True,
        use_trading_dates=True,
        experiment_track="test_track"
    )
    
    # 创建包含问题数据的状态
    problematic_state = {
        "current_date": "2025-01-06",
        "timestamp": pd.Timestamp("2025-01-06 10:30:00"),
        "price_history": {
            "AAPL": {
                pd.Timestamp("2025-01-05"): 245.0,
                pd.Timestamp("2025-01-06"): 246.0
            }
        },
        "news_history": {
            "2025-01-06": {
                "AAPL": [
                    {
                        "title": "Test News",
                        "timestamp": pd.Timestamp("2025-01-06 09:00:00"),
                        "sentiment": "Bullish"
                    }
                ]
            }
        },
        "positions": {"AAPL": np.int64(100)},
        "cash": np.float64(1000000.0)
    }
    
    # 测试记录输入
    try:
        input_id = logger.log_agent_input(
            agent_name="TEST_AGENT",
            state_data=problematic_state,
            market_data={"test": pd.Timestamp("2025-01-06")},
            previous_outputs=[{"result": "test"}],  # 故意传入列表而不是字典
            metadata={"test_timestamp": pd.Timestamp("2025-01-06")}
        )
        print(f"✅ 记录输入成功: {input_id}")
    except Exception as e:
        print(f"❌ 记录输入失败: {e}")
        return False
    
    # 测试记录提示词
    try:
        prompt_id = logger.log_agent_prompt(
            agent_name="TEST_AGENT",
            prompt_template="Test prompt",
            full_prompt="Full test prompt",
            metadata={"test_data": pd.Series([1, 2, 3])}
        )
        print(f"✅ 记录提示词成功: {prompt_id}")
    except Exception as e:
        print(f"❌ 记录提示词失败: {e}")
        return False
    
    # 测试记录输出
    try:
        output_id = logger.log_agent_output(
            agent_name="TEST_AGENT",
            input_id=input_id,
            prompt_id=prompt_id,
            raw_response={"timestamp": pd.Timestamp("2025-01-06"), "result": np.float64(0.85)},
            parsed_output={"confidence": np.float64(0.9), "signal": "buy"},
            processing_time=1.5,
            metadata={"processing_timestamp": pd.Timestamp("2025-01-06 10:35:00")}
        )
        print(f"✅ 记录输出成功: {output_id}")
    except Exception as e:
        print(f"❌ 记录输出失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试JSON序列化修复...")
    
    # 测试1: 基础序列化功能
    if not test_json_serialization():
        print("❌ 基础序列化测试失败")
        return False
    
    # 测试2: AgentInteractionLogger集成测试
    if not test_agent_logger_with_problematic_data():
        print("❌ AgentInteractionLogger集成测试失败")
        return False
    
    print("\n🎉 所有测试通过！JSON序列化修复成功！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
