2025-07-06 17:49:38,169 - __main__ - INFO - ====================================================================================================
2025-07-06 17:49:38,169 - __main__ - INFO - OPRO系统启动
2025-07-06 17:49:38,169 - __main__ - INFO - ====================================================================================================
2025-07-06 17:49:38,169 - __main__ - INFO - 运行模式: integrated
2025-07-06 17:49:38,170 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 17:49:38,170 - __main__ - INFO - OPRO启用: True
2025-07-06 17:49:38,170 - __main__ - INFO - 数据存储启用: True
2025-07-06 17:49:38,170 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 17:49:38,170 - __main__ - INFO - 初始化系统...
2025-07-06 17:49:38,171 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 17:49:38,171 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 17:49:38,171 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 17:49:38,171 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 17:49:38,172 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 17:49:38,172 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 17:49:38,177 - __main__ - INFO - 数据库初始化完成
2025-07-06 17:49:38,178 - __main__ - INFO - 自动备份线程已启动
2025-07-06 17:49:38,178 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:49:38,178 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 17:49:38,180 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 17:49:38,187 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:49:38,188 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:49:38,188 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 17:49:38,200 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:49:38,200 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:49:38,201 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 17:49:38,201 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 17:49:38,201 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 17:49:38,202 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 17:49:38,202 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 17:49:38,202 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 17:49:38,202 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 17:49:38,213 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:49:38,213 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:49:38,213 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:49:38,214 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:49:38,215 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 17:49:38,215 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 17:49:38,215 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 17:49:38,216 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 17:49:38,216 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 17:49:38,217 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:49:38,218 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 17:49:38,219 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:49:38,227 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:49:38,227 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:49:38,228 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 17:49:38,228 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:49:38,230 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:49:38,355 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:49:38,356 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:49:38,475 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:49:38,475 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:49:38,597 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 17:49:38,598 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:49:38,712 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 17:49:38,713 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 17:49:38,713 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 17:49:38,713 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 17:49:38,713 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 17:49:38,714 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:49:38,721 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:49:38,722 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:49:38,722 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:49:38,724 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:49:38,725 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:49:38,726 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:49:38,726 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:49:38,726 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:49:38,729 - __main__ - INFO - 加载历史数据完成: 7 个实验记录
2025-07-06 17:49:38,729 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:49:38,729 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:49:38,730 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:49:38,730 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 17:49:38,730 - __main__ - INFO - 系统初始化完成
2025-07-06 17:49:38,730 - __main__ - INFO - ================================================================================
2025-07-06 17:49:38,730 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 17:49:38,730 - __main__ - INFO - ================================================================================
2025-07-06 17:49:38,731 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-03-31
2025-07-06 17:49:38,731 - __main__ - INFO - ====================================================================================================
2025-07-06 17:49:38,731 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 17:49:38,731 - __main__ - INFO - ====================================================================================================
2025-07-06 17:49:38,731 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-03-31
2025-07-06 17:49:38,732 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:49:38,732 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 17:49:38,732 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 17:49:38,734 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-03-31
2025-07-06 17:49:38,735 - __main__ - INFO - 📊 总交易日数: 64
2025-07-06 17:49:38,735 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 17:49:38,735 - __main__ - INFO - 🗓️  最后交易日: 2025-03-31
2025-07-06 17:49:38,736 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 17:49:38,736 - __main__ - INFO - 📊 生成了 9 个7天周期
2025-07-06 17:49:38,736 - __main__ - INFO -    - 基线运行周: 5 个
2025-07-06 17:49:38,736 - __main__ - INFO -    - A/B测试周: 4 个
2025-07-06 17:49:38,737 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 17:49:38,737 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 17:49:38,737 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 17:49:38,737 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 17:49:38,738 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 17:49:38,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 17:49:38,738 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 17:49:38,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 17:49:38,738 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 17:49:38,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 17:49:38,739 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 17:49:38,739 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 17:49:38,739 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 17:49:38,739 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:49:38,739 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:49:38,739 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:49:38,744 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:49:38,744 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:49:38,744 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:49:38,745 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:49:38,745 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:49:38,745 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:49:38,745 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:49:38,745 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:49:38,745 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:49:38,746 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:49:38,746 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:49:38,746 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:49:38,746 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:49:38,746 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:49:38,746 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:49:38,747 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:49:38,747 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:49:38,747 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:49:38,747 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:49:38,748 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:49:38,748 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:49:38,748 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 17:49:38,748 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:49:38,749 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:49:38,749 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:49:38,749 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:49:38,749 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 17:49:38,749 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:49:38,750 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:49:38,754 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:38,755 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:38,756 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 17:49:38,760 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002690C6984A0>
2025-07-06 17:49:38,760 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 17:49:38,761 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:38,762 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 17:49:38,762 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:38,762 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 17:49:38,763 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 17:49:38,763 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002690C849650> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 17:49:38,980 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002690CA90170>
2025-07-06 17:49:38,980 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:38,981 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:38,981 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:38,981 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:38,981 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:39,240 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_174938\\prompts\\BeOA\\opt_BeOA_20250706_171136_7dc3b2aa.json'
2025-07-06 17:49:39,245 - contribution_assessment.assessor.ContributionAssessor - ERROR - 创建数据备份失败: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'data/backups\\backup_20250706_174938\\trading\\ab_test_results\\ab_config_2025-03-12.json'
2025-07-06 17:49:39,306 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据备份完成: backup_20250706_174938 (0.35 MB)
2025-07-06 17:49:41,371 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117517953796636198e0080d09d0c59457cb1aeccb80549be705b3b;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706174939a45caf587cb742b3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:41,372 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:41,372 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:41,372 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:41,372 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:41,374 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:41,375 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:41,378 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:41,379 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:41,379 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:41,380 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:41,380 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:41,380 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:41,380 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:44,367 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706174942434863ed91d64732'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:44,368 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:44,369 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:44,369 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:44,370 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:44,370 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:44,371 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:44,373 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:44,373 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:44,375 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:44,376 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:44,376 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:44,377 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:44,377 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:47,459 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706174945880f1a1906f04a4b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:47,459 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:47,460 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:47,461 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:47,461 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:47,461 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:47,462 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:47,464 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:47,464 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:47,466 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:47,467 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:47,467 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:47,468 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:47,468 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:50,999 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:51 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061749489db701743fc640c4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:51,000 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:51,000 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:51,001 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:51,002 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:51,002 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:51,002 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:51,004 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:51,005 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:51,006 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:51,007 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:51,008 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:51,008 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:51,008 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:53,485 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706174951c2df7b94e23f4cc1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:53,485 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:53,485 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:53,485 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:53,485 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:53,485 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:53,485 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:53,486 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:53,486 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:53,487 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:53,487 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:53,488 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:53,488 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:53,488 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:56,083 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617495486379eec88ba41e7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:56,084 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:56,084 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:56,085 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:56,085 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:56,085 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:56,086 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:56,088 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:56,088 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:56,090 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:56,090 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:56,091 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:56,092 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:56,092 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:49:58,551 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:49:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061749563565c97d1ddf47d4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:49:58,552 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:49:58,553 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:49:58,553 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:49:58,554 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:49:58,554 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:49:58,554 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:49:58,556 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:49:58,557 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:49:58,558 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:49:58,558 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:49:58,559 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:49:58,559 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:49:58,560 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:00,853 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706174959be81c7d0ae514a13'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:00,854 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:00,854 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:00,855 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:00,855 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:00,855 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:00,856 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:00,858 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:50:00,874 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175000_e32da3af
2025-07-06 17:50:00,874 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175000_e32da3af
2025-07-06 17:50:00,874 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175000_e32da3af
2025-07-06 17:50:00,874 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.669037
2025-07-06 17:50:00,874 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:50:00,875 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:50:00,875 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:50:00,876 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:00,876 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:00,877 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:00,877 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:00,878 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:00,878 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:00,878 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:03,071 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175001b81cc2682a29477c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:03,073 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:03,073 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:03,074 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:03,074 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:03,074 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:03,075 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:03,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:03,077 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:03,079 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:03,080 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:03,080 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:03,081 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:03,081 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:07,249 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175003febc81ca2db8425c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:07,249 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:07,249 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:07,250 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:07,250 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:07,250 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:07,250 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:07,251 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:07,251 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:07,252 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:07,252 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:07,253 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:07,253 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:07,253 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:10,698 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175007534be24a3cbe4ec3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:10,700 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:10,700 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:10,701 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:10,701 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:10,701 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:10,702 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:10,704 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:10,704 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:10,705 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:10,706 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:10,707 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:10,707 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:10,707 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:13,853 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:14 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750117fb1bd51d25b49de'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:13,855 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:13,855 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:13,856 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:13,857 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:13,857 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:13,858 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:13,860 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:13,860 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:13,862 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:13,863 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:13,863 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:13,863 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:13,863 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:17,828 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:18 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617501462247103969044cd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:17,828 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:17,828 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:17,829 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:17,829 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:17,829 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:17,829 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:17,830 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:17,830 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:17,831 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:17,831 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:17,831 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:17,831 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:17,831 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:19,932 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175018981bfe06640b4bbf'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:19,932 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:19,934 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:19,935 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:19,935 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:19,936 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:19,936 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:19,938 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:19,939 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:19,940 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:19,941 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:19,941 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:19,942 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:19,942 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:23,231 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750209997a871e1b341d5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:23,232 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:23,233 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:23,234 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:23,234 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:23,234 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:23,234 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:23,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:23,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:23,239 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:23,240 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:23,240 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:23,241 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:23,241 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:26,774 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175023c9e016dae53941ee'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:26,774 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:26,775 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:26,776 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:26,776 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:26,777 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:26,777 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:26,779 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:50:26,789 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175026_e24faac9
2025-07-06 17:50:26,790 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175026_e24faac9
2025-07-06 17:50:26,790 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175026_e24faac9
2025-07-06 17:50:26,791 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.831194
2025-07-06 17:50:26,791 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:50:26,809 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 17:50:26,809 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:50:26,809 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:50:26,809 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 17:50:26,810 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 17:50:26,810 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 17:50:26,810 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 17:50:26,810 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 17:50:26,810 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 17:50:26,810 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 17:50:26,810 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 17:50:26,810 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 17:50:26,810 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 17:50:26,810 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 17:50:26,811 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 17:50:26,811 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 17:50:26,811 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 17:50:26,811 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:50:26,811 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:50:26,811 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:50:26,811 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:50:26,811 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:50:26,812 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:50:26,812 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:50:26,812 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:50:26,812 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_175026
2025-07-06 17:50:26,812 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_175026 - 0 条记录
2025-07-06 17:50:26,813 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_175026
2025-07-06 17:50:26,813 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:50:26,813 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 17:50:26,813 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-16
2025-07-06 17:50:26,813 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 17:50:26,813 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 17:50:26,813 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第2/7天
2025-07-06 17:50:26,813 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 17:50:26,813 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 17:50:26,814 - __main__ - INFO - ✅ 第 2 周完成: ab_testing_complete
2025-07-06 17:50:26,814 - __main__ - INFO - 🔬 第 2 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:50:26,814 - __main__ - WARNING - ⚠️  第 2 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:50:26,814 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 17:50:26,814 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 17:50:26,814 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 17:50:26,814 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 17:50:26,814 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 17:50:26,814 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:50:26,814 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:50:26,814 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:50:26,818 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:50:26,819 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:50:26,819 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:50:26,819 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:50:26,819 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:50:26,819 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:50:26,819 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:50:26,819 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:50:26,819 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:50:26,820 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:50:26,820 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:50:26,820 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:50:26,820 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:50:26,820 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:50:26,820 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:50:26,820 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:50:26,821 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:50:26,821 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:50:26,821 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:50:26,821 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:50:26,821 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:50:26,821 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 17:50:26,822 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:50:26,822 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:50:26,822 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:50:26,822 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:50:26,822 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-16
2025-07-06 17:50:26,822 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:50:26,822 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:50:26,823 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:26,823 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:26,824 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:26,824 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:26,825 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:26,825 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:26,825 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:29,065 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750270c17f345b3be4cb4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:29,065 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:29,066 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:29,066 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:29,067 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:29,067 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:29,068 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:29,069 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:29,070 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:29,072 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:29,072 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:29,073 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:29,074 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:29,074 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:31,472 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175029685ff91fa5b2491b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:31,473 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:31,473 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:31,474 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:31,475 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:31,475 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:31,476 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:31,478 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:31,478 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:31,480 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:31,481 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:31,481 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:31,481 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:31,481 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:33,784 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617503296e503051ae04d70'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:33,785 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:33,785 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:33,786 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:33,787 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:33,787 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:33,787 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:33,788 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:33,788 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:33,789 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:33,789 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:33,790 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:33,790 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:33,790 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:36,255 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617503471e2297f171547d6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:36,255 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:36,255 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:36,255 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:36,257 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:36,257 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:36,257 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:36,257 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:36,258 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:36,258 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:36,258 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:36,259 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:36,259 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:36,259 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:38,877 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175036c5766c0ce7774a6f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:38,878 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:38,879 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:38,879 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:38,880 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:38,880 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:38,880 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:38,883 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:38,883 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:38,884 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:38,885 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:38,885 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:38,886 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:38,886 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:41,726 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750396eeb530387634bcb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:41,727 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:41,727 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:41,728 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:41,728 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:41,728 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:41,728 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:41,729 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:41,729 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:41,730 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:41,730 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:41,731 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:41,731 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:41,731 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:44,880 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175042a6bf17fca1f34c59'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:44,881 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:44,881 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:44,881 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:44,882 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:44,882 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:44,882 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:44,885 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:44,885 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:44,887 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:44,888 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:44,888 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:44,889 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:44,889 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:47,024 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750455521befc30d04f7b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:47,024 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:47,025 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:47,026 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:47,026 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:47,027 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:47,027 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:47,029 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:50:47,037 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175047_69e86aff
2025-07-06 17:50:47,037 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175047_69e86aff
2025-07-06 17:50:47,038 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175047_69e86aff
2025-07-06 17:50:47,038 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.662889
2025-07-06 17:50:47,038 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:50:47,038 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:50:47,038 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:50:47,040 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:47,040 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:47,040 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:47,041 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:47,041 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:47,041 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:47,041 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:49,983 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617504769768559a31a4e61'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:49,983 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:49,983 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:49,984 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:49,984 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:49,984 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:49,984 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:49,986 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:49,986 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:49,987 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:49,987 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:49,987 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:49,987 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:49,988 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:53,416 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061750504a40748eba794466'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:53,417 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:53,418 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:53,418 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:53,419 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:53,419 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:53,419 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:53,421 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:53,421 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:53,423 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:53,423 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:53,424 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:53,425 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:53,425 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:57,187 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:50:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175054b5ce8f4a74274799'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:57,188 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:57,188 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:57,189 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:57,190 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:57,190 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:57,190 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:57,192 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:57,192 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:57,193 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:57,194 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:57,195 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:57,195 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:57,195 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:50:59,902 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175057b85d86e4414d427e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:50:59,904 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:50:59,904 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:50:59,904 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:50:59,905 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:50:59,905 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:50:59,905 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:50:59,906 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:50:59,906 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:50:59,906 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:50:59,908 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:50:59,908 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:50:59,908 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:50:59,908 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:02,791 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175100973626e745f04c9c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:02,793 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:02,793 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:02,794 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:02,794 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:02,794 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:02,794 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:02,796 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:02,796 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:02,798 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:02,799 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:02,799 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:02,800 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:02,800 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:06,131 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175103264275b2b10b4729'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:06,132 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:06,133 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:06,134 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:06,134 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:06,135 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:06,135 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:06,137 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:06,138 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:06,139 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:06,140 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:06,140 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:06,140 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:06,141 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:08,828 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175106bad11825d0a14d88'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:08,829 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:08,830 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:08,831 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:08,831 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:08,831 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:08,832 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:08,833 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:08,833 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:08,835 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:08,835 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:08,835 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:08,836 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:08,836 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:12,701 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751097bf123e5a87a4886'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:12,703 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:12,703 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:12,704 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:12,704 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:12,705 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:12,705 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:12,707 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:51:12,717 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175112_fc07205d
2025-07-06 17:51:12,717 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175112_fc07205d
2025-07-06 17:51:12,717 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175112_fc07205d
2025-07-06 17:51:12,717 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.811470
2025-07-06 17:51:12,718 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:51:12,738 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-16
2025-07-06 17:51:12,739 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:51:12,739 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:51:12,739 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 17:51:12,739 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 17:51:12,739 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-24
2025-07-06 17:51:12,740 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 17:51:12,740 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 17:51:12,740 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第4/7天
2025-07-06 17:51:12,740 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 17:51:12,740 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第5/7天
2025-07-06 17:51:12,741 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 17:51:12,741 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第6/7天
2025-07-06 17:51:12,741 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 17:51:12,741 - __main__ - INFO - 🔄 开始第 4 周（ab_testing）: 2025-01-30 到 2025-02-07
2025-07-06 17:51:12,741 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-30
2025-07-06 17:51:12,741 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第7/7天
2025-07-06 17:51:12,741 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:51:12,741 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:51:12,742 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:51:12,742 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:51:12,742 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:51:12,742 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:51:12,742 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:51:12,742 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:51:12,742 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_175112
2025-07-06 17:51:12,742 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_175112 - 0 条记录
2025-07-06 17:51:12,744 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_175112
2025-07-06 17:51:12,744 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:51:12,744 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-31
2025-07-06 17:51:12,744 - __main__ - INFO - 🆕 开始第5周 - 基线运行阶段: 2025-01-31
2025-07-06 17:51:12,744 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第1/7天
2025-07-06 17:51:12,744 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-03
2025-07-06 17:51:12,744 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第4/7天
2025-07-06 17:51:12,744 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-04
2025-07-06 17:51:12,745 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第5/7天
2025-07-06 17:51:12,745 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-05
2025-07-06 17:51:12,745 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第6/7天
2025-07-06 17:51:12,745 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-06
2025-07-06 17:51:12,745 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第7/7天
2025-07-06 17:51:12,745 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:51:12,746 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:51:12,746 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:51:12,749 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:51:12,751 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:51:12,751 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:51:12,751 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:51:12,751 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:51:12,751 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:51:12,752 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:51:12,752 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:51:12,752 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:51:12,752 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:51:12,752 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:51:12,752 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:51:12,752 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:51:12,753 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:51:12,753 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:51:12,753 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:51:12,753 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:51:12,753 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:51:12,753 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:51:12,753 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:51:12,753 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:51:12,754 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 17:51:12,754 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:51:12,754 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:51:12,755 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:51:12,755 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:51:12,755 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-31
2025-07-06 17:51:12,755 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:51:12,755 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:51:12,756 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:12,756 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:12,757 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:12,757 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:12,758 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:12,758 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:12,758 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:15,408 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617511316ded3e7d4be4e91'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:15,408 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:15,409 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:15,410 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:15,411 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:15,411 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:15,411 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:15,414 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:15,414 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:15,416 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:15,416 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:15,416 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:15,417 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:15,417 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:17,890 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:18 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751165771178d9dfd4023'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:17,891 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:17,891 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:17,892 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:17,892 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:17,893 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:17,893 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:17,895 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:17,895 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:17,897 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:17,897 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:17,897 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:17,897 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:17,897 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:19,822 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175118377a0ad8624c4f40'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:19,822 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:19,822 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:19,822 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:19,822 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:19,823 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:19,823 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:19,824 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:19,824 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:19,825 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:19,825 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:19,825 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:19,825 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:19,825 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:22,281 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175120b932fa55286d4c25'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:22,282 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:22,282 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:22,282 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:22,283 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:22,283 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:22,284 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:22,286 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:22,286 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:22,287 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:22,288 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:22,288 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:22,289 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:22,289 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:25,295 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175122448e5945add94e81'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:25,296 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:25,296 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:25,296 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:25,297 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:25,297 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:25,298 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:25,300 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:25,300 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:25,302 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:25,303 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:25,303 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:25,304 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:25,304 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:27,457 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:28 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175125b6476ff6b06b4d82'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:27,458 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:27,459 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:27,460 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:27,460 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:27,460 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:27,461 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:27,463 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:27,463 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:27,465 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:27,465 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:27,466 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:27,467 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:27,467 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:29,946 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751285c46c946d9074881'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:29,946 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:29,947 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:29,948 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:29,948 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:29,948 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:29,948 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:29,950 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:29,951 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:29,952 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:29,952 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:29,953 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:29,953 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:29,953 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:31,827 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751308cb5bb707be240f1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:31,828 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:31,829 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:31,829 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:31,830 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:31,830 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:31,831 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:31,833 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:51:31,841 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175131_c19149a7
2025-07-06 17:51:31,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175131_c19149a7
2025-07-06 17:51:31,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175131_c19149a7
2025-07-06 17:51:31,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.735620
2025-07-06 17:51:31,842 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:51:31,843 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:51:31,843 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:51:31,843 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:31,844 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:31,844 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:31,845 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:31,845 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:31,845 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:31,845 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:34,208 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175132260738b9933c4909'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:34,209 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:34,210 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:34,210 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:34,211 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:34,211 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:34,211 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:34,213 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:34,213 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:34,214 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:34,214 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:34,215 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:34,215 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:34,215 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:37,437 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:38 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175134ac0ae234c52d4793'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:37,438 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:37,439 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:37,439 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:37,439 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:37,440 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:37,440 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:37,441 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:37,441 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:37,443 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:37,443 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:37,443 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:37,445 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:37,445 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:40,281 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751382c05533a9565441d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:40,282 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:40,283 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:40,283 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:40,283 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:40,284 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:40,284 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:40,286 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:40,287 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:40,288 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:40,289 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:40,289 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:40,290 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:40,290 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:43,445 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175140a3b4b401c7b643c4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:43,446 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:43,447 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:43,448 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:43,449 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:43,449 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:43,449 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:43,452 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:43,452 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:43,453 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:43,455 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:43,455 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:43,455 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:43,455 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:46,482 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751448d67e6f457d347c9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:46,483 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:46,484 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:46,484 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:46,485 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:46,485 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:46,485 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:46,488 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:46,488 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:46,490 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:46,491 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:46,492 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:46,492 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:46,492 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:49,484 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751470afb43ac25b4438e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:49,486 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:49,486 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:49,487 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:49,487 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:49,488 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:49,488 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:49,491 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:49,491 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:49,493 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:49,493 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:49,493 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:49,494 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:49,494 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:53,017 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175150580be2af5fbd47fe'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:53,018 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:53,019 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:53,019 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:53,019 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:53,020 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:53,020 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:53,021 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:53,022 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:53,023 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:53,023 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:53,024 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:53,025 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:53,025 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:56,633 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:51:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617515387a032ca4a0849bf'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:56,634 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:56,634 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:56,635 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:56,636 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:56,636 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:56,636 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:56,638 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:51:56,648 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175156_6f814b6e
2025-07-06 17:51:56,648 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175156_6f814b6e
2025-07-06 17:51:56,648 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175156_6f814b6e
2025-07-06 17:51:56,649 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.902444
2025-07-06 17:51:56,649 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:51:56,670 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-31
2025-07-06 17:51:56,671 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:51:56,671 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:51:56,671 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-07
2025-07-06 17:51:56,671 - __main__ - INFO - ✅ 第 4 周完成: baseline_complete
2025-07-06 17:51:56,671 - __main__ - INFO - 🔄 开始第 5 周（baseline_operation）: 2025-02-10 到 2025-02-18
2025-07-06 17:51:56,671 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-10
2025-07-06 17:51:56,671 - __main__ - INFO - 🆕 开始第6周 - A/B测试阶段: 2025-02-10
2025-07-06 17:51:56,672 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第1/7天
2025-07-06 17:51:56,672 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-11
2025-07-06 17:51:56,672 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第2/7天
2025-07-06 17:51:56,672 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-12
2025-07-06 17:51:56,672 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第3/7天
2025-07-06 17:51:56,672 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-13
2025-07-06 17:51:56,672 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第4/7天
2025-07-06 17:51:56,672 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-14
2025-07-06 17:51:56,672 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第5/7天
2025-07-06 17:51:56,672 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-17
2025-07-06 17:51:56,673 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-18
2025-07-06 17:51:56,673 - __main__ - INFO - 🆕 开始第7周 - 基线运行阶段: 2025-02-18
2025-07-06 17:51:56,673 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第1/7天
2025-07-06 17:51:56,673 - __main__ - INFO - ✅ 第 5 周完成: in_progress
2025-07-06 17:51:56,673 - __main__ - INFO - 🔄 开始第 6 周（ab_testing）: 2025-02-19 到 2025-02-27
2025-07-06 17:51:56,673 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-19
2025-07-06 17:51:56,673 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第2/7天
2025-07-06 17:51:56,673 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-20
2025-07-06 17:51:56,673 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第3/7天
2025-07-06 17:51:56,673 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-21
2025-07-06 17:51:56,673 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第4/7天
2025-07-06 17:51:56,674 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-24
2025-07-06 17:51:56,674 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第7/7天
2025-07-06 17:51:56,674 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:51:56,674 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:51:56,674 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:51:56,677 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:51:56,677 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:51:56,678 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:51:56,678 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:51:56,678 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:51:56,678 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:51:56,678 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:51:56,678 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:51:56,678 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:51:56,678 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:51:56,679 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:51:56,679 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:51:56,679 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:51:56,679 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:51:56,679 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:51:56,679 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:51:56,679 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:51:56,680 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:51:56,680 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:51:56,680 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:51:56,680 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:51:56,680 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-06 17:51:56,680 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:51:56,681 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:51:56,681 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:51:56,681 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:51:56,681 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-02-18
2025-07-06 17:51:56,682 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:51:56,682 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:51:56,683 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:56,684 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:56,684 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:56,685 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:56,686 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:56,686 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:56,686 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:51:59,443 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061751579c876258b790453d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:51:59,444 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:51:59,444 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:51:59,445 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:51:59,445 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:51:59,445 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:51:59,445 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:51:59,447 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:51:59,447 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:51:59,447 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:51:59,447 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:51:59,449 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:51:59,449 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:51:59,449 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:02,415 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175200826089ce77e246d7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:02,416 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:02,416 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:02,417 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:02,418 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:02,418 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:02,418 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:02,420 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:02,420 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:02,422 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:02,422 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:02,422 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:02,424 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:02,424 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:06,800 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617520322db34e2606f44ec'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:06,800 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:06,800 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:06,800 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:06,800 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:06,801 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:06,801 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:06,802 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:06,802 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:06,803 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:06,803 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:06,804 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:06,804 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:06,804 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:08,837 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752070b3d29b42ccc4fb2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:08,838 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:08,838 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:08,839 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:08,839 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:08,840 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:08,840 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:08,842 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:08,842 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:08,845 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:08,846 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:08,846 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:08,847 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:08,847 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:11,406 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617520987924837fa31426c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:11,407 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:11,407 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:11,408 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:11,408 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:11,408 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:11,409 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:11,410 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:11,411 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:11,412 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:11,413 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:11,413 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:11,415 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:11,415 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:14,536 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752122c2347f2eedd4d35'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:14,537 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:14,538 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:14,538 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:14,539 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:14,539 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:14,539 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:14,542 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:14,542 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:14,544 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:14,545 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:14,545 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:14,546 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:14,546 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:17,225 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752151cd197b45a0144d3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:17,226 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:17,227 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:17,227 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:17,227 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:17,227 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:17,227 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:17,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:17,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:17,230 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:17,230 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:17,230 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:17,230 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:17,231 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:21,300 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175217d103aad31ce14220'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:21,301 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:21,301 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:21,302 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:21,303 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:21,303 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:21,303 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:21,305 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:52:21,312 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175221_63a807d5
2025-07-06 17:52:21,312 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175221_63a807d5
2025-07-06 17:52:21,312 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175221_63a807d5
2025-07-06 17:52:21,313 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.720692
2025-07-06 17:52:21,313 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:52:21,313 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:52:21,313 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:52:21,314 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:21,314 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:21,314 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:21,314 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:21,315 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:21,315 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:21,315 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:25,184 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175222472d85d83a234e04'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:25,186 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:25,186 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:25,187 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:25,187 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:25,188 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:25,188 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:25,190 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:25,190 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:25,191 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:25,191 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:25,191 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:25,192 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:25,192 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:26,963 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617522522b6cf0793b940cb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:26,964 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:26,964 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:26,965 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:26,965 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:26,965 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:26,966 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:26,967 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:26,967 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:26,968 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:26,969 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:26,970 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:26,970 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:26,970 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:29,416 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752274f52005de23048fd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:29,416 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:29,416 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:29,417 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:29,417 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:29,417 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:29,417 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:29,418 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:29,418 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:29,419 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:29,419 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:29,420 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:29,420 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:29,420 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:32,360 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175230b2816055f0c14543'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:32,361 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:32,361 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:32,361 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:32,362 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:32,362 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:32,362 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:32,364 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:32,365 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:32,366 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:32,367 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:32,368 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:32,369 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:32,369 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:34,468 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617523360fe3d0e86bf4c1c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:34,469 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:34,469 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:34,469 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:34,470 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:34,470 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:34,470 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:34,471 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:34,471 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:34,471 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:34,472 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:34,472 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:34,472 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:34,472 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:37,549 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:38 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752355e652dcd077c4af9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:37,550 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:37,551 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:37,552 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:37,552 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:37,552 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:37,553 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:37,554 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:37,555 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:37,556 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:37,557 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:37,558 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:37,559 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:37,559 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:40,992 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752386eadf23cb44445de'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:40,993 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:40,993 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:40,994 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:40,994 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:40,995 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:40,995 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:40,997 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:40,997 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:40,999 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:40,999 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:41,000 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:41,001 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:41,001 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:44,510 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175241e672d3fd1e704c92'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:44,511 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:44,511 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:44,512 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:44,512 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:44,513 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:44,513 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:44,515 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:52:44,522 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175244_169d6216
2025-07-06 17:52:44,524 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175244_169d6216
2025-07-06 17:52:44,524 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175244_169d6216
2025-07-06 17:52:44,525 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.688975
2025-07-06 17:52:44,525 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:52:44,537 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-02-18
2025-07-06 17:52:44,537 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:52:44,538 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:52:44,538 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-25
2025-07-06 17:52:44,538 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-26
2025-07-06 17:52:44,538 - __main__ - INFO - 🆕 开始第8周 - A/B测试阶段: 2025-02-26
2025-07-06 17:52:44,538 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第1/7天
2025-07-06 17:52:44,538 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-27
2025-07-06 17:52:44,538 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第2/7天
2025-07-06 17:52:44,539 - __main__ - INFO - ✅ 第 6 周完成: baseline_complete
2025-07-06 17:52:44,539 - __main__ - INFO - 🔄 开始第 7 周（baseline_operation）: 2025-02-28 到 2025-03-10
2025-07-06 17:52:44,539 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-28
2025-07-06 17:52:44,539 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第3/7天
2025-07-06 17:52:44,539 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-03
2025-07-06 17:52:44,539 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第6/7天
2025-07-06 17:52:44,539 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-04
2025-07-06 17:52:44,539 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第7/7天
2025-07-06 17:52:44,539 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:52:44,539 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:52:44,539 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:52:44,540 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:52:44,540 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:52:44,540 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:52:44,540 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:52:44,540 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:52:44,541 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_175244
2025-07-06 17:52:44,541 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_175244 - 0 条记录
2025-07-06 17:52:44,541 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_175244
2025-07-06 17:52:44,541 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:52:44,541 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-05
2025-07-06 17:52:44,541 - __main__ - INFO - 🆕 开始第9周 - 基线运行阶段: 2025-03-05
2025-07-06 17:52:44,542 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第1/7天
2025-07-06 17:52:44,542 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-06
2025-07-06 17:52:44,542 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第2/7天
2025-07-06 17:52:44,542 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-07
2025-07-06 17:52:44,542 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第3/7天
2025-07-06 17:52:44,542 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-10
2025-07-06 17:52:44,542 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第6/7天
2025-07-06 17:52:44,542 - __main__ - INFO - ✅ 第 7 周完成: ab_testing_complete
2025-07-06 17:52:44,542 - __main__ - INFO - 🔬 第 7 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:52:44,542 - __main__ - WARNING - ⚠️  第 7 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:52:44,542 - __main__ - INFO - 🔄 开始第 8 周（ab_testing）: 2025-03-11 到 2025-03-19
2025-07-06 17:52:44,542 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-11
2025-07-06 17:52:44,542 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第7/7天
2025-07-06 17:52:44,542 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:52:44,542 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:52:44,542 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:52:44,547 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:52:44,547 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:52:44,547 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:52:44,547 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:52:44,547 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:52:44,547 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:52:44,548 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:52:44,548 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:52:44,548 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:52:44,548 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:52:44,548 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:52:44,548 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:52:44,549 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:52:44,549 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:52:44,549 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:52:44,549 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:52:44,549 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 17:52:44,549 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:52:44,549 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:52:44,549 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:52:44,550 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:52:44,550 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-05
2025-07-06 17:52:44,550 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:52:44,550 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:52:44,551 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:44,551 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:44,552 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:44,552 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:44,552 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:44,552 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:44,552 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:46,967 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175245d723577b5ea54bc3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:46,969 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:46,969 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:46,970 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:46,970 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:46,970 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:46,971 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:46,973 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:46,973 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:46,975 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:46,976 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:46,976 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:46,977 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:46,977 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:49,417 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752474bdca51429104a31'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:49,418 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:49,418 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:49,419 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:49,419 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:49,420 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:49,420 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:49,422 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:49,422 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:49,424 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:49,425 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:49,425 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:49,426 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:49,426 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:51,647 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175250271320d6678e4ed2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:51,648 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:51,649 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:51,650 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:51,650 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:51,650 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:51,651 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:51,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:51,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:51,654 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:51,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:51,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:51,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:51,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:54,226 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617525298931af872754fa3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:54,226 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:54,227 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:54,227 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:54,228 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:54,228 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:54,228 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:54,230 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:54,231 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:54,232 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:54,232 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:54,232 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:54,234 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:54,235 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:52:58,002 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:52:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175254a476d527e1404d90'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:52:58,004 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:52:58,004 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:52:58,005 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:52:58,006 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:52:58,006 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:52:58,006 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:52:58,009 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:52:58,009 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:52:58,011 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:52:58,011 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:52:58,011 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:52:58,012 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:52:58,012 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:00,929 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061752582710bff2eb33416a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:00,930 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:00,930 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:00,930 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:00,930 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:00,930 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:00,931 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:00,931 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:00,932 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:00,932 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:00,933 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:00,933 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:00,933 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:00,933 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:03,424 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175301bda2d1a663254b99'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:03,425 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:03,425 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:03,426 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:03,426 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:03,427 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:03,427 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:03,429 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:03,429 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:03,431 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:03,431 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:03,432 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:03,433 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:03,433 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:06,147 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175304d2ba5ec2ec1f44d8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:06,148 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:06,148 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:06,148 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:06,148 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:06,149 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:06,149 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:06,150 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:53:06,156 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175306_9d99a2ad
2025-07-06 17:53:06,156 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175306_9d99a2ad
2025-07-06 17:53:06,157 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175306_9d99a2ad
2025-07-06 17:53:06,157 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.688957
2025-07-06 17:53:06,157 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:53:06,157 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:53:06,157 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:53:06,158 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:06,158 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:06,159 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:06,159 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:06,160 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:06,160 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:06,160 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:09,133 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175306b2b78d4fc9a44113'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:09,133 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:09,134 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:09,134 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:09,135 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:09,135 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:09,135 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:09,138 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:09,138 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:09,140 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:09,141 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:09,141 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:09,142 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:09,142 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:14,817 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175309f09ebe606cce4da9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:14,818 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:14,818 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:14,818 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:14,818 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:14,818 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:14,818 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:14,819 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:14,819 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:14,820 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:14,820 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:14,820 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:14,820 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:14,820 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:18,085 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:18 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061753156e176d51d1404095'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:18,086 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:18,087 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:18,088 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:18,088 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:18,089 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:18,089 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:18,090 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:18,091 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:18,092 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:18,092 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:18,092 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:18,094 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:18,094 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:20,121 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175318f09f64010efa46ea'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:20,122 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:20,123 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:20,124 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:20,124 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:20,125 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:20,125 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:20,127 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:20,127 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:20,128 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:20,128 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:20,129 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:20,129 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:20,129 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:23,395 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061753205af55f73be9d47e8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:23,396 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:23,396 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:23,396 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:23,397 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:23,397 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:23,397 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:23,399 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:23,400 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:23,401 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:23,402 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:23,403 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:23,403 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:23,404 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:26,614 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175324329d5a6e1f724070'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:26,615 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:26,616 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:26,616 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:26,617 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:26,617 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:26,617 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:26,618 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:26,619 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:26,620 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:26,620 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:26,621 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:26,621 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:26,621 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:28,934 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175327d44804003d264593'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:28,935 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:28,935 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:28,936 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:28,937 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:28,937 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:28,938 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:28,939 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:28,940 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:28,941 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:28,942 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:28,943 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:28,943 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:28,943 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:31,499 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175329a1a5787997b34816'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:31,500 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:31,500 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:31,500 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:31,501 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:31,501 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:31,501 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:31,502 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:53:31,511 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175331_1e5c0a74
2025-07-06 17:53:31,512 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175331_1e5c0a74
2025-07-06 17:53:31,512 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175331_1e5c0a74
2025-07-06 17:53:31,512 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.771450
2025-07-06 17:53:31,513 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:53:31,528 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-03-05
2025-07-06 17:53:31,528 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:53:31,528 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:53:31,528 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-12
2025-07-06 17:53:31,528 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-13
2025-07-06 17:53:31,529 - __main__ - INFO - 🆕 开始第10周 - A/B测试阶段: 2025-03-13
2025-07-06 17:53:31,529 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第1/7天
2025-07-06 17:53:31,529 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-14
2025-07-06 17:53:31,529 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第2/7天
2025-07-06 17:53:31,529 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-17
2025-07-06 17:53:31,529 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第5/7天
2025-07-06 17:53:31,529 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-18
2025-07-06 17:53:31,529 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第6/7天
2025-07-06 17:53:31,529 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-19
2025-07-06 17:53:31,529 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第7/7天
2025-07-06 17:53:31,529 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:53:31,529 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:53:31,530 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:53:31,530 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:53:31,530 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:53:31,530 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:53:31,530 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:53:31,530 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:53:31,530 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_175331
2025-07-06 17:53:31,531 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_175331 - 0 条记录
2025-07-06 17:53:31,531 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_175331
2025-07-06 17:53:31,531 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:53:31,531 - __main__ - INFO - ✅ 第 8 周完成: ab_testing_complete
2025-07-06 17:53:31,531 - __main__ - INFO - 🔬 第 8 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:53:31,531 - __main__ - WARNING - ⚠️  第 8 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:53:31,531 - __main__ - INFO - 🔄 开始第 9 周（baseline_operation）: 2025-03-20 到 2025-03-28
2025-07-06 17:53:31,531 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-20
2025-07-06 17:53:31,532 - __main__ - INFO - 🆕 开始第11周 - 基线运行阶段: 2025-03-20
2025-07-06 17:53:31,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第1/7天
2025-07-06 17:53:31,532 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-21
2025-07-06 17:53:31,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第2/7天
2025-07-06 17:53:31,532 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-24
2025-07-06 17:53:31,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第5/7天
2025-07-06 17:53:31,532 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-25
2025-07-06 17:53:31,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第6/7天
2025-07-06 17:53:31,532 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-26
2025-07-06 17:53:31,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第7/7天
2025-07-06 17:53:31,532 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:53:31,532 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:53:31,533 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:53:31,535 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:53:31,535 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:53:31,535 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:53:31,536 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:53:31,536 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:53:31,536 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:53:31,536 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:53:31,536 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.031298
2025-07-06 17:53:31,536 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:53:31,536 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.020457
2025-07-06 17:53:31,536 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:53:31,537 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.052001
2025-07-06 17:53:31,537 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:53:31,537 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.005977
2025-07-06 17:53:31,537 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:53:31,537 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.008080
2025-07-06 17:53:31,537 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:53:31,537 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.043609
2025-07-06 17:53:31,537 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:53:31,538 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.050775
2025-07-06 17:53:31,538 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.212198 = 大联盟值 0.212198
2025-07-06 17:53:31,538 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-06 17:53:31,538 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:53:31,538 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:53:31,538 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:53:31,539 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:53:31,539 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-20
2025-07-06 17:53:31,539 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:53:31,539 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:53:31,540 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:31,540 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:31,540 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:31,541 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:31,542 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:31,542 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:31,542 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:34,857 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617533235c2f45ac4524be5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:34,859 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:34,859 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:34,860 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:34,861 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:34,861 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:34,862 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:34,864 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:34,864 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:34,866 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:34,867 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:34,867 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:34,867 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:34,867 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:37,226 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175335d39d22b5deec4c17'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:37,227 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:37,227 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:37,228 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:37,228 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:37,228 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:37,228 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:37,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:37,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:37,230 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:37,230 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:37,231 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:37,231 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:37,231 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:41,627 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175337bf97eb8850b24258'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:41,628 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:41,629 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:41,630 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:41,630 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:41,630 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:41,631 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:41,633 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:41,633 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:41,634 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:41,634 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:41,634 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:41,634 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:41,634 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:44,635 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175342784d0b33466d4f69'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:44,635 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:44,636 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:44,636 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:44,638 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:44,638 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:44,638 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:44,640 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:44,640 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:44,642 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:44,642 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:44,642 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:44,643 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:44,643 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:46,951 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175345c113b15a994f4cd1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:46,953 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:46,954 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:46,954 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:46,954 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:46,955 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:46,955 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:46,956 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:46,956 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:46,957 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:46,958 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:46,958 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:46,959 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:46,959 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:50,716 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:51 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061753471abe96b1c2d0497e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:50,718 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:50,718 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:50,719 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:50,719 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:50,719 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:50,720 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:50,722 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:50,722 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:50,723 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:50,724 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:50,725 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:50,725 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:50,726 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:53,785 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617535166d128429b86469e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:53,785 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:53,787 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:53,788 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:53,788 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:53,788 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:53,789 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:53,790 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:53,791 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:53,792 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:53,793 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:53,793 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:53,793 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:53,793 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:53:58,103 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:53:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617535426c65a60f8484e08'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:53:58,104 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:53:58,104 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:53:58,104 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:53:58,104 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:53:58,104 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:53:58,105 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:53:58,106 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:53:58,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_175358_e995e078
2025-07-06 17:53:58,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_175358_e995e078
2025-07-06 17:53:58,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_175358_e995e078
2025-07-06 17:53:58,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.744552
2025-07-06 17:53:58,112 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:53:58,114 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:53:58,114 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:53:58,115 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:53:58,115 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:53:58,116 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:53:58,116 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:53:58,116 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:53:58,116 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:53:58,117 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:01,689 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175358c56071dbbd4b4cbe'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:01,690 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:01,690 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:01,691 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:01,692 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:01,692 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:01,693 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:01,695 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:01,696 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:01,697 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:01,698 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:01,698 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:01,698 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:01,699 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:04,245 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175402c4bbf06bd0074d3b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:04,246 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:04,246 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:04,247 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:04,247 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:04,247 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:04,247 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:04,249 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:04,249 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:04,250 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:04,250 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:04,250 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:04,250 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:04,251 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:08,770 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061754043477e171230a4519'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:08,771 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:08,771 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:08,771 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:08,771 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:08,771 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:08,772 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:08,772 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:08,772 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:08,773 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:08,773 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:08,773 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:08,773 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:08,773 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:11,647 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175409b59b1046775c4a70'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:11,648 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:11,649 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:11,650 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:11,651 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:11,651 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:11,652 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:11,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:11,654 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:11,656 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:11,657 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:11,658 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:11,658 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:11,658 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:15,530 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061754120c57e6636ede4be4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:15,530 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:15,531 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:15,531 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:15,531 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:15,531 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:15,532 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:15,532 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:15,532 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:15,533 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:15,533 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:15,533 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:15,533 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:15,533 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:18,667 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175416beb1600682654d3b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:18,668 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:18,668 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:18,669 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:18,669 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:18,669 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:18,670 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:18,672 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:18,672 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:18,672 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:18,674 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:18,674 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:18,674 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:18,675 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:21,824 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061754193437a0a92abf461d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:21,825 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:21,825 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:21,826 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:21,826 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:21,826 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:21,827 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:21,827 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:54:21,828 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:54:21,829 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:54:21,829 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:54:21,830 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:54:21,830 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:54:21,831 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:54:24,959 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:54:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706175422615f04788e644537'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:54:24,960 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:54:24,961 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:54:24,961 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:54:24,961 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:54:24,962 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:54:24,962 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:54:24,963 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:54:24,970 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_175424_47bb1479
2025-07-06 17:54:24,970 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_175424_47bb1479
2025-07-06 17:54:24,971 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_175424_47bb1479
2025-07-06 17:54:24,971 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.735308
2025-07-06 17:54:24,972 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:54:24,987 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-03-20
2025-07-06 17:54:24,987 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:54:24,987 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:54:24,987 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-27
2025-07-06 17:54:24,988 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-28
2025-07-06 17:54:24,988 - __main__ - INFO - 🆕 开始第12周 - A/B测试阶段: 2025-03-28
2025-07-06 17:54:24,988 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第12周，第1/7天
2025-07-06 17:54:24,988 - __main__ - INFO - ✅ 第 9 周完成: baseline_complete
2025-07-06 17:54:24,988 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 17:54:24,988 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 17:54:24,989 - __main__ - INFO -   - 总周期数: 9
2025-07-06 17:54:24,989 - __main__ - INFO -   - 成功周期: 8
2025-07-06 17:54:24,989 - __main__ - INFO -   - 失败周期: 1
2025-07-06 17:54:24,989 - __main__ - INFO -   - 成功率: 88.89%
2025-07-06 17:54:24,989 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 17:54:24,989 - __main__ - INFO - ====================================================================================================
2025-07-06 17:54:24,990 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 17:54:24,990 - __main__ - INFO - 📊 总周期数: 9
2025-07-06 17:54:24,990 - __main__ - INFO - 📊 总交易天数: 64
2025-07-06 17:54:24,990 - __main__ - INFO - ⏱️  总执行时间: 286.26秒
2025-07-06 17:54:24,990 - __main__ - INFO - ====================================================================================================
2025-07-06 17:54:24,990 - __main__ - INFO - 开始交易会话: assessment_20250706_175424
2025-07-06 17:54:24,990 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 17:54:24,994 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_175424
2025-07-06 17:54:24,995 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_175424 (系统盈亏: 0.00)
2025-07-06 17:54:24,995 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_175424
2025-07-06 17:54:24,995 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 17:54:24,995 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 17:54:24,995 - __main__ - INFO - ====================================================================================================
2025-07-06 17:54:24,995 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 17:54:24,995 - __main__ - INFO - ====================================================================================================
