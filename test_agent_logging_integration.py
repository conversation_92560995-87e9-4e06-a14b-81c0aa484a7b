#!/usr/bin/env python3
"""
测试智能体交互日志集成
验证修复后的系统是否能正确记录智能体交互日志
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_agent_logging_integration():
    """测试智能体交互日志集成"""
    logger = setup_logging()
    logger.info("🧪 开始测试智能体交互日志集成")
    
    try:
        # 1. 导入必要的模块
        from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 2. 创建测试配置
        test_config = {
            "llm_provider": "zhipuai",
            "opro": {
                "enabled": True,
                "max_iterations": 3,
                "temperature": 0.7
            },
            "enhanced_shapley_oprp": {
                "enabled": True,
                "cycle_length_days": 7,
                "underperforming_threshold": 0.3
            },
            "agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"],
            "simulation_days": 1,
            "max_coalitions": 5
        }
        
        # 3. 创建智能体交互日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="data/trading",
            enabled=True
        )
        interaction_logger.set_experiment_date("2025-01-06")
        interaction_logger.set_experiment_track("original_track")
        
        # 4. 创建增强的OPRO管理器
        logger.info("📋 创建增强的OPRO管理器...")
        manager = EnhancedWeeklyOPROManager(
            config=test_config,
            base_data_dir="data/trading",
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        # 5. 测试每日交易决策执行
        logger.info("🤖 测试每日交易决策执行...")
        target_agents = ["NAA", "TAA", "TRA"]
        daily_results = {}
        
        result = manager._execute_daily_trading_decisions(
            current_date="2025-01-06",
            target_agents=target_agents,
            daily_results=daily_results
        )
        
        # 6. 检查结果
        logger.info(f"📊 执行结果: {result.get('success', False)}")
        if result.get("success"):
            logger.info(f"✅ 测试成功 - 联盟数量: {result.get('total_coalitions_tested', 0)}")
            logger.info(f"⏱️ 执行时间: {result.get('execution_time', 0):.2f}秒")
        else:
            logger.warning(f"⚠️ 测试失败 - 错误: {result.get('error', '未知错误')}")
        
        # 7. 检查是否生成了智能体交互日志文件
        logger.info("📁 检查智能体交互日志文件...")
        log_dir = Path("data/trading/2025-01-06")
        
        if log_dir.exists():
            logger.info(f"✅ 日志目录存在: {log_dir}")
            
            # 检查各个智能体的日志文件
            for agent_name in target_agents:
                agent_dir = log_dir / agent_name
                if agent_dir.exists():
                    logger.info(f"📂 智能体 {agent_name} 日志目录存在")
                    
                    # 检查具体的日志文件
                    for log_file in ["inputs.json", "prompts.json", "outputs.json"]:
                        log_path = agent_dir / log_file
                        if log_path.exists():
                            logger.info(f"  ✅ {log_file} 存在 (大小: {log_path.stat().st_size} 字节)")
                            
                            # 显示文件内容的前几行
                            try:
                                with open(log_path, 'r', encoding='utf-8') as f:
                                    content = json.load(f)
                                    logger.info(f"  📄 {log_file} 记录数量: {len(content)}")
                            except Exception as e:
                                logger.warning(f"  ⚠️ 读取 {log_file} 失败: {e}")
                        else:
                            logger.warning(f"  ❌ {log_file} 不存在")
                else:
                    logger.warning(f"❌ 智能体 {agent_name} 日志目录不存在")
        else:
            logger.warning(f"❌ 日志目录不存在: {log_dir}")
        
        return result.get("success", False)
        
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_agent_logging_integration()
    if success:
        print("\n🎉 智能体交互日志集成测试成功！")
    else:
        print("\n💥 智能体交互日志集成测试失败！")
        sys.exit(1)
