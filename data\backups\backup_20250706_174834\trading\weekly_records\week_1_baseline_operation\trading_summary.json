{"week_number": 1, "week_type": "baseline_operation", "completion_timestamp": "2025-07-06T17:47:16.491246", "summary_data": {"week_info": {"week_id": "week_1_baseline_operation_20250706_174716", "week_number": 1, "week_type": "baseline_operation", "start_date": "2025-01-01", "end_date": "2025-01-07", "target_agents": ["TRA", "NSA", "FSA"], "started_at": "2025-07-06T17:47:16.456422"}, "weekly_summary": {"status": "baseline_complete", "week_number": 1, "phase": "baseline_operation", "shapley_analysis": {"shapley_values": {"TRA": 0.08384327594103397, "NSA": 0.040436001721889256, "FSA": 0.04972212268287165}, "calculation_metadata": {"calculation_date": "2025-07-06", "target_agents": ["TRA", "NSA", "FSA"], "data_source": "winning_experiments", "data_quality_report": {"agent_data_coverage": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "data_freshness": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "data_consistency": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "overall_quality": 0.0}, "coalition_count": 8, "config": {"use_only_winning_data": true, "min_data_points_per_agent": 5, "data_quality_threshold": 0.8, "enable_historical_tracking": true, "enable_data_validation": true, "max_historical_weeks": 12, "outlier_detection_enabled": true, "outlier_threshold": 2.0}}, "data_quality_report": {"agent_data_coverage": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "data_freshness": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "data_consistency": {"TRA": 0.0, "NSA": 0.0, "FSA": 0.0}, "overall_quality": 0.0}, "success": true}, "underperforming_agents": ["NSA"], "optimization_result": {"status": "error", "error": "OPRO优化器未初始化"}, "next_phase": "ab_testing", "next_week_target_agents": ["NSA"]}, "integration_completed_at": "2025-07-06T17:47:16.491246"}, "data_statistics": {"total_agent_decisions": 0, "decision_type_distribution": {"buy": 0, "sell": 0, "hold": 0}, "agents_with_data": [], "performance_metrics_recorded": true, "ab_test_tracks": []}, "files_created": ["week_config.json", "performance_data\\performance_metrics_baseline.json"], "status": "completed"}