2025-07-06 20:11:14,065 - __main__ - INFO - ====================================================================================================
2025-07-06 20:11:14,066 - __main__ - INFO - OPRO系统启动
2025-07-06 20:11:14,066 - __main__ - INFO - ====================================================================================================
2025-07-06 20:11:14,066 - __main__ - INFO - 运行模式: integrated
2025-07-06 20:11:14,066 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 20:11:14,066 - __main__ - INFO - OPRO启用: True
2025-07-06 20:11:14,066 - __main__ - INFO - 数据存储启用: True
2025-07-06 20:11:14,066 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 20:11:14,068 - __main__ - INFO - 初始化系统...
2025-07-06 20:11:14,068 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 20:11:14,068 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 20:11:14,068 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 20:11:14,069 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 20:11:14,069 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:11:14,069 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:11:14,071 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:11:14,071 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:11:14,072 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:14,072 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 20:11:14,072 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 20:11:14,082 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:14,082 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:14,082 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 20:11:14,094 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 20:11:14,094 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 20:11:14,094 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 20:11:14,096 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 20:11:14,096 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 20:11:14,097 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 20:11:14,097 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 20:11:14,097 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 20:11:14,097 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 20:11:14,109 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:11:14,109 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 20:11:14,109 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 20:11:14,109 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:11:14,109 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 20:11:14,109 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 20:11:14,110 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:11:14,110 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 20:11:14,110 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:14,111 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 20:11:14,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 20:11:14,112 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:14,119 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:14,120 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:14,120 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 20:11:14,121 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:11:14,122 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:11:14,252 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:11:14,253 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:11:14,373 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:11:14,374 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:11:14,494 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 20:11:14,495 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 20:11:14,597 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 20:11:14,597 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 20:11:14,597 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 20:11:14,597 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 20:11:14,598 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 20:11:14,598 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 20:11:14,603 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 20:11:14,604 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 20:11:14,604 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:14,606 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 20:11:14,609 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 20:11:14,609 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 20:11:14,609 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:14,609 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:11:14,612 - __main__ - INFO - 加载历史数据完成: 15 个实验记录
2025-07-06 20:11:14,612 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 20:11:14,613 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 20:11:14,613 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 20:11:14,613 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 20:11:14,613 - __main__ - INFO - 系统初始化完成
2025-07-06 20:11:14,613 - __main__ - INFO - ================================================================================
2025-07-06 20:11:14,613 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 20:11:14,614 - __main__ - INFO - ================================================================================
2025-07-06 20:11:14,614 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-01-08
2025-07-06 20:11:14,614 - __main__ - INFO - ====================================================================================================
2025-07-06 20:11:14,614 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 20:11:14,614 - __main__ - INFO - ====================================================================================================
2025-07-06 20:11:14,614 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-08
2025-07-06 20:11:14,614 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:14,615 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 20:11:14,615 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 20:11:14,616 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-08
2025-07-06 20:11:14,616 - __main__ - INFO - 📊 总交易日数: 6
2025-07-06 20:11:14,616 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 20:11:14,616 - __main__ - INFO - 🗓️  最后交易日: 2025-01-08
2025-07-06 20:11:14,617 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 20:11:14,617 - __main__ - INFO - 📊 生成了 1 个7天周期
2025-07-06 20:11:14,617 - __main__ - INFO -    - 基线运行周: 1 个
2025-07-06 20:11:14,617 - __main__ - INFO -    - A/B测试周: 0 个
2025-07-06 20:11:14,617 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 20:11:14,617 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-08
2025-07-06 20:11:14,618 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 20:11:14,618 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 20:11:14,618 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 20:11:14,618 - __main__ - INFO - 📊 执行每日交易决策 - 第1周，第1/7天
2025-07-06 20:11:14,618 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-01
2025-07-06 20:11:14,618 - __main__ - INFO - 分析缓存初始化完成
2025-07-06 20:11:14,619 - __main__ - INFO - 联盟管理器初始化完成
2025-07-06 20:11:14,619 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:11:14,619 - __main__ - INFO - 交易模拟器初始化完成
2025-07-06 20:11:14,620 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:14,620 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:11:14,620 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:11:14,620 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:11:14,621 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:11:14,621 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:11:14,621 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:11:14,632 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:11:14,633 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:11:14,633 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:14,648 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:14,649 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:14,649 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-06 20:11:14,649 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 禁用)
2025-07-06 20:11:14,650 - __main__ - INFO - 📊 执行智能体联盟交易模拟 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:14,650 - __main__ - WARNING - LLM接口不可用，无法创建LLM智能体
2025-07-06 20:11:14,650 - __main__ - INFO - 开始贡献度评估流程
2025-07-06 20:11:14,651 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:14,651 - __main__ - INFO - 可用智能体实例: 无（将使用模拟智能体）
2025-07-06 20:11:14,653 - __main__ - INFO - 开始新实验: experiment_20250706_201114
2025-07-06 20:11:14,653 - __main__ - INFO - 开始新实验: experiment_20250706_201114
2025-07-06 20:11:14,653 - __main__ - INFO - ==================================================
2025-07-06 20:11:14,653 - __main__ - INFO - 阶段1: 分析缓存
2025-07-06 20:11:14,654 - __main__ - WARNING - LLM接口不可用且无智能体实例，使用模拟数据...
2025-07-06 20:11:14,654 - __main__ - INFO - 开始分析缓存阶段...
2025-07-06 20:11:14,654 - __main__ - WARNING - 未提供分析智能体实例，将使用模拟数据
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-06 20:11:14,654 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-06 20:11:14,654 - __main__ - INFO - ==================================================
2025-07-06 20:11:14,656 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-06 20:11:14,656 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-06 20:11:14,656 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-06 20:11:14,656 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-06 20:11:14,656 - __main__ - INFO - ============================================================
2025-07-06 20:11:14,656 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-06 20:11:14,656 - __main__ - INFO - ============================================================
2025-07-06 20:11:14,657 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-06 20:11:14,657 - __main__ - INFO - 开始联盟生成阶段...
2025-07-06 20:11:14,657 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-06 20:11:14,657 - __main__ - INFO - 总智能体数: 7
2025-07-06 20:11:14,657 - __main__ - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-06 20:11:14,657 - __main__ - INFO - 交易智能体: TRA
2025-07-06 20:11:14,658 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-06 20:11:14,658 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-06 20:11:14,658 - __main__ - INFO - 联盟剪枝完成:
2025-07-06 20:11:14,658 - __main__ - INFO -   - 总联盟数: 128
2025-07-06 20:11:14,658 - __main__ - INFO -   - 有效联盟: 56
2025-07-06 20:11:14,659 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-06 20:11:14,659 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-06 20:11:14,659 - __main__ - INFO -   - 生成耗时: 0.001s
2025-07-06 20:11:14,659 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-06 20:11:14,659 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-06 20:11:14,660 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-06 20:11:14,660 - __main__ - INFO - 开始交易模拟阶段...
2025-07-06 20:11:14,660 - __main__ - INFO - 限制模拟联盟数量: 56 -> 20
2025-07-06 20:11:14,660 - __main__ - INFO - 启用并发模拟：20 个联盟，最大并发数：30
2025-07-06 20:11:14,661 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:14,663 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:14,663 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,666 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,666 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,666 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,666 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,667 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:14,667 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,668 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:14,672 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:14,672 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:14,675 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,675 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:14,678 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:14,680 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:14,685 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:14,687 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:14,688 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,700 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,703 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:14,703 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:14,703 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:14,704 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:14,704 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:14,708 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,716 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:14,718 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,721 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:14,722 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:14,722 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:14,723 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:14,734 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,734 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-06 20:11:14,737 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,746 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:14,753 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:14,768 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:14,772 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-06 20:11:14,779 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,009 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,010 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,011 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:15,012 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,013 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,013 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,013 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,014 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,014 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TRA
2025-07-06 20:11:15,017 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TRA
2025-07-06 20:11:15,023 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TRA
2025-07-06 20:11:15,024 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (0.36s)
2025-07-06 20:11:15,153 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,154 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,154 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:15,155 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,155 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,156 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,156 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,157 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,158 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_NOA_TRA
2025-07-06 20:11:15,161 - __main__ - INFO - 创建联盟存储: FAA_NAA_NOA_TRA
2025-07-06 20:11:15,173 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,177 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,178 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,179 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,180 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,180 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,181 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,181 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,181 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_NOA_TRA
2025-07-06 20:11:15,182 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:15,187 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.52s)
2025-07-06 20:11:15,190 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,194 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,194 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:15,195 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,195 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,196 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,196 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,196 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,196 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,197 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,198 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:15,203 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.53s)
2025-07-06 20:11:15,205 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,208 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,210 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,216 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:15,220 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,221 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,221 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,229 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,237 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,237 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,238 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,238 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,239 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,239 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,239 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,240 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.57s)
2025-07-06 20:11:15,241 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:15,241 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,242 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,268 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,283 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,288 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,294 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,299 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,300 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,300 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,301 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,301 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,301 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,302 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,302 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,302 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,303 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,309 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:15,310 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,317 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,318 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,318 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,319 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,320 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,320 - __main__ - INFO - 创建联盟存储: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,320 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,321 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,321 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,321 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,321 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,322 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:15,322 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,324 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,326 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,327 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,327 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,328 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,329 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,336 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,337 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,337 - __main__ - WARNING - 联盟目录不存在，创建: FAA_TAA_TRA
2025-07-06 20:11:15,337 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,344 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,345 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,345 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,346 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,346 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:15,346 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,347 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,347 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 0.0000 (0.67s)
2025-07-06 20:11:15,348 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,348 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,349 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,349 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,350 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,350 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,351 - __main__ - INFO - 创建联盟存储: FAA_TAA_TRA
2025-07-06 20:11:15,351 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,352 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,352 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,353 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_TAA_TRA
2025-07-06 20:11:15,353 - __main__ - WARNING - 联盟目录不存在，创建: NOA_TAA_TRA
2025-07-06 20:11:15,353 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:15,353 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,354 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,354 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:15,354 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,357 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:15,358 - __main__ - INFO - 保存联盟模拟结果: FAA_TAA_TRA
2025-07-06 20:11:15,358 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,358 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,360 - __main__ - INFO - 创建联盟存储: BeOA_TAA_TRA
2025-07-06 20:11:15,361 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:15,363 - __main__ - INFO - 创建联盟存储: NOA_TAA_TRA
2025-07-06 20:11:15,364 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,364 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,366 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,367 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:15,368 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,369 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,369 - __main__ - INFO - 保存联盟模拟结果: BeOA_TAA_TRA
2025-07-06 20:11:15,369 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_TAA_TRA
2025-07-06 20:11:15,370 - __main__ - INFO - 保存联盟模拟结果: NOA_TAA_TRA
2025-07-06 20:11:15,370 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.68s)
2025-07-06 20:11:15,371 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:15,372 - __main__ - INFO - 创建联盟存储: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,372 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:15,372 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,374 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.67s)
2025-07-06 20:11:15,375 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.71s)
2025-07-06 20:11:15,376 - __main__ - INFO - 创建联盟存储: BOA_BeOA_TAA_TRA
2025-07-06 20:11:15,377 - __main__ - INFO - 📊 并发进度: 10/20 (50.0%)
2025-07-06 20:11:15,378 - __main__ - INFO - 保存联盟模拟结果: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,380 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.71s)
2025-07-06 20:11:15,384 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_TAA_TRA
2025-07-06 20:11:15,384 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.68s)
2025-07-06 20:11:15,398 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,399 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,399 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,399 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,400 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,400 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,400 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,400 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,401 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,403 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,420 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,420 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,421 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-06 20:11:15,427 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,428 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,429 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,429 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,430 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:15,430 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,436 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,436 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,437 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.73s)
2025-07-06 20:11:15,454 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,454 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,468 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,471 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,486 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:15,486 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,488 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,488 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,488 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,488 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,488 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,489 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,489 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:15,489 - __main__ - WARNING - 联盟目录不存在，创建: TAA_TRA
2025-07-06 20:11:15,489 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,489 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,489 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,490 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,490 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,490 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,490 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,490 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:15,491 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,491 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,491 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,491 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,491 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:15,492 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_TRA
2025-07-06 20:11:15,492 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,492 - __main__ - INFO - 创建联盟存储: TAA_TRA
2025-07-06 20:11:15,492 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,492 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,492 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:15,492 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,492 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,493 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,494 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,496 - __main__ - INFO - 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,497 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,498 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,498 - __main__ - INFO - 保存联盟模拟结果: TAA_TRA
2025-07-06 20:11:15,498 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:15,499 - __main__ - INFO - 创建联盟存储: FAA_NAA_TRA
2025-07-06 20:11:15,499 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_TAA_TRA
2025-07-06 20:11:15,499 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,499 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (0.77s)
2025-07-06 20:11:15,499 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_TRA
2025-07-06 20:11:15,500 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,503 - __main__ - INFO - 创建联盟存储: BOA_NAA_TAA_TRA
2025-07-06 20:11:15,503 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_TRA
2025-07-06 20:11:15,503 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,503 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,503 - __main__ - INFO - 创建联盟存储: BeOA_FAA_TRA
2025-07-06 20:11:15,504 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.80s)
2025-07-06 20:11:15,508 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_TAA_TRA
2025-07-06 20:11:15,511 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 0.0000 (0.79s)
2025-07-06 20:11:15,512 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:15,512 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:15,513 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.79s)
2025-07-06 20:11:15,513 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.79s)
2025-07-06 20:11:15,513 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_TRA
2025-07-06 20:11:15,514 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.78s)
2025-07-06 20:11:15,514 - __main__ - INFO - 📊 并发进度: 20/20 (100.0%)
2025-07-06 20:11:15,515 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 20 个，失败 0 个，总耗时 0.86s
2025-07-06 20:11:15,515 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-06 20:11:15,516 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:15,516 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:15,516 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:15,516 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:15,516 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:15,516 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:15,516 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:15,516 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:15,517 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:15,517 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:15,517 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:15,517 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:15,517 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:15,517 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:15,517 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:15,517 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:15,518 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:15,518 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:15,518 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:15,518 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:15,518 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:15,518 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 20:11:15,518 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:15,518 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-06 20:11:15,518 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-06 20:11:15,518 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 20 个
2025-07-06 20:11:15,518 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-06 20:11:15,519 - __main__ - INFO - ==================================================
2025-07-06 20:11:15,519 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-06 20:11:15,519 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:15,519 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:15,519 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:15,519 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:15,519 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:15,519 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:15,519 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:15,519 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:15,519 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:15,520 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:15,520 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:15,520 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:15,520 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:15,520 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:15,520 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:15,520 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:15,520 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:15,520 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:15,520 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:15,521 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:15,521 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:15,521 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 20:11:15,522 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:15,522 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-06 20:11:15,523 - __main__ - INFO - ==================================================
2025-07-06 20:11:15,523 - __main__ - INFO - 贡献度评估完成，总耗时: 0.87s
2025-07-06 20:11:15,523 - __main__ - INFO - ✅ 每日交易决策执行成功 - 日期: 2025-01-01
2025-07-06 20:11:15,523 - __main__ - INFO - 📈 最佳联盟: N/A
2025-07-06 20:11:15,524 - __main__ - INFO - 📊 夏普比率: N/A
2025-07-06 20:11:15,524 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 20:11:15,525 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 20:11:15,525 - __main__ - INFO - 📊 执行每日交易决策 - 第1周，第2/7天
2025-07-06 20:11:15,525 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-02
2025-07-06 20:11:15,525 - __main__ - INFO - 分析缓存初始化完成
2025-07-06 20:11:15,526 - __main__ - INFO - 联盟管理器初始化完成
2025-07-06 20:11:15,526 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:11:15,526 - __main__ - INFO - 交易模拟器初始化完成
2025-07-06 20:11:15,526 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:15,527 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:11:15,527 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:11:15,528 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:11:15,528 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:11:15,528 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:11:15,528 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:11:15,529 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:11:15,530 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:11:15,530 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:15,548 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:15,550 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:15,550 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-06 20:11:15,550 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 禁用)
2025-07-06 20:11:15,550 - __main__ - INFO - 📊 执行智能体联盟交易模拟 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:15,552 - __main__ - WARNING - LLM接口不可用，无法创建LLM智能体
2025-07-06 20:11:15,552 - __main__ - INFO - 开始贡献度评估流程
2025-07-06 20:11:15,552 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:15,552 - __main__ - INFO - 可用智能体实例: 无（将使用模拟智能体）
2025-07-06 20:11:15,554 - __main__ - INFO - 开始新实验: experiment_20250706_201115
2025-07-06 20:11:15,554 - __main__ - INFO - 开始新实验: experiment_20250706_201115
2025-07-06 20:11:15,554 - __main__ - INFO - ==================================================
2025-07-06 20:11:15,555 - __main__ - INFO - 阶段1: 分析缓存
2025-07-06 20:11:15,555 - __main__ - WARNING - LLM接口不可用且无智能体实例，使用模拟数据...
2025-07-06 20:11:15,555 - __main__ - INFO - 开始分析缓存阶段...
2025-07-06 20:11:15,555 - __main__ - WARNING - 未提供分析智能体实例，将使用模拟数据
2025-07-06 20:11:15,555 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-06 20:11:15,555 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-06 20:11:15,555 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-06 20:11:15,555 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-06 20:11:15,556 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-06 20:11:15,556 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-06 20:11:15,556 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-06 20:11:15,556 - __main__ - INFO - ==================================================
2025-07-06 20:11:15,556 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-06 20:11:15,556 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-06 20:11:15,556 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-06 20:11:15,557 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-06 20:11:15,557 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,558 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-06 20:11:15,558 - __main__ - INFO - ============================================================
2025-07-06 20:11:15,558 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-06 20:11:15,558 - __main__ - INFO - 开始联盟生成阶段...
2025-07-06 20:11:15,558 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-06 20:11:15,558 - __main__ - INFO - 总智能体数: 7
2025-07-06 20:11:15,558 - __main__ - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-06 20:11:15,558 - __main__ - INFO - 交易智能体: TRA
2025-07-06 20:11:15,558 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-06 20:11:15,558 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-06 20:11:15,559 - __main__ - INFO - 联盟剪枝完成:
2025-07-06 20:11:15,560 - __main__ - INFO -   - 总联盟数: 128
2025-07-06 20:11:15,560 - __main__ - INFO -   - 有效联盟: 56
2025-07-06 20:11:15,560 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-06 20:11:15,560 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-06 20:11:15,561 - __main__ - INFO -   - 生成耗时: 0.001s
2025-07-06 20:11:15,561 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-06 20:11:15,561 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-06 20:11:15,561 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-06 20:11:15,561 - __main__ - INFO - 开始交易模拟阶段...
2025-07-06 20:11:15,561 - __main__ - INFO - 限制模拟联盟数量: 56 -> 20
2025-07-06 20:11:15,561 - __main__ - INFO - 启用并发模拟：20 个联盟，最大并发数：30
2025-07-06 20:11:15,563 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:15,565 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:15,566 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,567 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,567 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,569 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,569 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,569 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:15,571 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:15,571 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,571 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:15,571 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,571 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:15,579 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,585 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:15,586 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,589 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:15,598 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,601 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,604 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,604 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:15,604 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:15,616 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,620 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:15,620 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,621 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,626 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,626 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:15,634 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:15,636 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,637 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-06 20:11:15,637 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:15,638 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,638 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:15,639 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,652 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:15,663 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:15,666 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:15,670 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-06 20:11:15,686 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,054 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,060 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,062 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,063 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,064 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:16,065 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:16,065 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,065 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,066 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,066 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,066 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,066 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,067 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,071 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,078 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,086 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,096 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,101 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,116 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,121 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,134 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,142 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,154 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,168 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,172 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,179 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,180 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TRA
2025-07-06 20:11:16,202 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,215 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,218 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,221 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,230 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,239 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,240 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,241 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,242 - __main__ - INFO - 创建联盟存储: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,243 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,243 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,243 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,244 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,244 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,246 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,246 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,246 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,247 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TRA
2025-07-06 20:11:16,247 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,247 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:16,247 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:16,249 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,251 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,259 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,265 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,271 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,278 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,284 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,287 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,288 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,288 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:16,289 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,290 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,290 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,292 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,292 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,293 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,299 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,299 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TRA
2025-07-06 20:11:16,303 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,316 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,321 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 0.0000 (0.73s)
2025-07-06 20:11:16,346 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,353 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,360 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,374 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,382 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:16,383 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,384 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,384 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,384 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,384 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,384 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,386 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,386 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,386 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,386 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,386 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,387 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (0.82s)
2025-07-06 20:11:16,387 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,387 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,388 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,388 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,388 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,388 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,388 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,388 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,388 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:16,388 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,389 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,389 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,389 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,390 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,390 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,390 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,390 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,390 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,391 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,391 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:16,392 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,392 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,393 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,393 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,393 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-06 20:11:16,394 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,394 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,394 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,394 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,396 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,397 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,397 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,397 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,399 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,400 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,400 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,401 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,402 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,402 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,402 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,403 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,403 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,404 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:16,404 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,404 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,404 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,405 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,405 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,406 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,407 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,408 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,409 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_TAA_TRA
2025-07-06 20:11:16,410 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,412 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,413 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,413 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,413 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,413 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,414 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,414 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,416 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:16,417 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,417 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,419 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,419 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,420 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,421 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_NOA_TRA
2025-07-06 20:11:16,421 - __main__ - WARNING - 联盟目录不存在，创建: NOA_TAA_TRA
2025-07-06 20:11:16,422 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:16,423 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:16,424 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,424 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,425 - __main__ - INFO - 创建联盟存储: BeOA_TAA_TRA
2025-07-06 20:11:16,425 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,425 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,427 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,427 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,427 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:16,428 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_TAA_TRA
2025-07-06 20:11:16,428 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,429 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,429 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,430 - __main__ - WARNING - 联盟目录不存在，创建: FAA_TAA_TRA
2025-07-06 20:11:16,430 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:16,433 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,435 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,438 - __main__ - INFO - 创建联盟存储: FAA_NAA_NOA_TRA
2025-07-06 20:11:16,438 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:16,438 - __main__ - INFO - 创建联盟存储: NOA_TAA_TRA
2025-07-06 20:11:16,439 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,439 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,440 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,440 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:16,441 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,442 - __main__ - INFO - 保存联盟模拟结果: BeOA_TAA_TRA
2025-07-06 20:11:16,442 - __main__ - INFO - 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,443 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,445 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,447 - __main__ - INFO - 创建联盟存储: BOA_BeOA_TAA_TRA
2025-07-06 20:11:16,447 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,450 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,451 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,451 - __main__ - INFO - 创建联盟存储: FAA_TAA_TRA
2025-07-06 20:11:16,451 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,451 - __main__ - INFO - 创建联盟存储: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,452 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:16,452 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,453 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:16,453 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.85s)
2025-07-06 20:11:16,453 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_TAA_TRA
2025-07-06 20:11:16,454 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:16,454 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.89s)
2025-07-06 20:11:16,454 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_TRA
2025-07-06 20:11:16,455 - __main__ - INFO - 保存联盟模拟结果: NOA_TAA_TRA
2025-07-06 20:11:16,456 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_NOA_TRA
2025-07-06 20:11:16,456 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,458 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_TRA
2025-07-06 20:11:16,459 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,459 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:16,461 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_TAA_TRA
2025-07-06 20:11:16,464 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.90s)
2025-07-06 20:11:16,466 - __main__ - WARNING - 联盟目录不存在，创建: TAA_TRA
2025-07-06 20:11:16,468 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,469 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.87s)
2025-07-06 20:11:16,469 - __main__ - INFO - 保存联盟模拟结果: FAA_TAA_TRA
2025-07-06 20:11:16,470 - __main__ - INFO - 保存联盟模拟结果: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,471 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (0.89s)
2025-07-06 20:11:16,472 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:16,472 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.90s)
2025-07-06 20:11:16,472 - __main__ - INFO - 创建联盟存储: BOA_NAA_TAA_TRA
2025-07-06 20:11:16,474 - __main__ - INFO - 创建联盟存储: FAA_NAA_TRA
2025-07-06 20:11:16,476 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.90s)
2025-07-06 20:11:16,477 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.86s)
2025-07-06 20:11:16,482 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,483 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,485 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 0.0000 (0.91s)
2025-07-06 20:11:16,486 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.91s)
2025-07-06 20:11:16,486 - __main__ - INFO - 创建联盟存储: BeOA_FAA_TRA
2025-07-06 20:11:16,487 - __main__ - INFO - 创建联盟存储: TAA_TRA
2025-07-06 20:11:16,488 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.92s)
2025-07-06 20:11:16,491 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,492 - __main__ - INFO - 📊 并发进度: 10/20 (50.0%)
2025-07-06 20:11:16,496 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_TAA_TRA
2025-07-06 20:11:16,498 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.87s)
2025-07-06 20:11:16,498 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:16,498 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_TRA
2025-07-06 20:11:16,499 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 0.0000 (0.86s)
2025-07-06 20:11:16,500 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:16,501 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.88s)
2025-07-06 20:11:16,501 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.90s)
2025-07-06 20:11:16,501 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_TRA
2025-07-06 20:11:16,502 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.87s)
2025-07-06 20:11:16,502 - __main__ - INFO - 保存联盟模拟结果: TAA_TRA
2025-07-06 20:11:16,503 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.87s)
2025-07-06 20:11:16,504 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (0.87s)
2025-07-06 20:11:16,504 - __main__ - INFO - 📊 并发进度: 20/20 (100.0%)
2025-07-06 20:11:16,506 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 20 个，失败 0 个，总耗时 0.94s
2025-07-06 20:11:16,507 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-06 20:11:16,508 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:16,508 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:16,508 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:16,508 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:16,508 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:16,509 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:16,509 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:16,509 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:16,510 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:16,510 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:16,510 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:16,511 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:16,512 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:16,514 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:16,515 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:16,516 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:16,516 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:16,517 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:16,518 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:16,518 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:16,518 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:16,518 - __main__ - INFO - Shapley值计算完成，耗时 0.010s
2025-07-06 20:11:16,519 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:16,519 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-06 20:11:16,519 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-06 20:11:16,520 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 20 个
2025-07-06 20:11:16,520 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-06 20:11:16,520 - __main__ - INFO - ==================================================
2025-07-06 20:11:16,520 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-06 20:11:16,520 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:16,521 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:16,521 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:16,521 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:16,521 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:16,522 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:16,522 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:16,522 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:16,523 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:16,523 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:16,523 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:16,524 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:16,524 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:16,524 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:16,524 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:16,525 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:16,525 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:16,525 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:16,526 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:16,526 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:16,527 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:16,527 - __main__ - INFO - Shapley值计算完成，耗时 0.006s
2025-07-06 20:11:16,528 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:16,528 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-06 20:11:16,529 - __main__ - INFO - ==================================================
2025-07-06 20:11:16,529 - __main__ - INFO - 贡献度评估完成，总耗时: 0.98s
2025-07-06 20:11:16,529 - __main__ - INFO - ✅ 每日交易决策执行成功 - 日期: 2025-01-02
2025-07-06 20:11:16,530 - __main__ - INFO - 📈 最佳联盟: N/A
2025-07-06 20:11:16,530 - __main__ - INFO - 📊 夏普比率: N/A
2025-07-06 20:11:16,531 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 20:11:16,532 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 20:11:16,533 - __main__ - INFO - 📊 执行每日交易决策 - 第1周，第3/7天
2025-07-06 20:11:16,534 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-03
2025-07-06 20:11:16,535 - __main__ - INFO - 分析缓存初始化完成
2025-07-06 20:11:16,536 - __main__ - INFO - 联盟管理器初始化完成
2025-07-06 20:11:16,537 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:11:16,538 - __main__ - INFO - 交易模拟器初始化完成
2025-07-06 20:11:16,539 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:16,539 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:11:16,540 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:11:16,540 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:11:16,541 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:11:16,541 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:11:16,542 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:11:16,545 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:11:16,547 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:11:16,549 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:16,584 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:16,584 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:16,584 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-06 20:11:16,584 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 禁用)
2025-07-06 20:11:16,584 - __main__ - INFO - 📊 执行智能体联盟交易模拟 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:16,586 - __main__ - WARNING - LLM接口不可用，无法创建LLM智能体
2025-07-06 20:11:16,586 - __main__ - INFO - 开始贡献度评估流程
2025-07-06 20:11:16,586 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:16,586 - __main__ - INFO - 可用智能体实例: 无（将使用模拟智能体）
2025-07-06 20:11:16,588 - __main__ - INFO - 开始新实验: experiment_20250706_201116
2025-07-06 20:11:16,589 - __main__ - INFO - 开始新实验: experiment_20250706_201116
2025-07-06 20:11:16,589 - __main__ - INFO - ==================================================
2025-07-06 20:11:16,589 - __main__ - INFO - 阶段1: 分析缓存
2025-07-06 20:11:16,590 - __main__ - WARNING - LLM接口不可用且无智能体实例，使用模拟数据...
2025-07-06 20:11:16,590 - __main__ - INFO - 开始分析缓存阶段...
2025-07-06 20:11:16,590 - __main__ - WARNING - 未提供分析智能体实例，将使用模拟数据
2025-07-06 20:11:16,590 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-06 20:11:16,591 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-06 20:11:16,591 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-06 20:11:16,592 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-06 20:11:16,592 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-06 20:11:16,592 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-06 20:11:16,592 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-06 20:11:16,593 - __main__ - INFO - ==================================================
2025-07-06 20:11:16,593 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-06 20:11:16,593 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-06 20:11:16,593 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-06 20:11:16,594 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-06 20:11:16,594 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,594 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-06 20:11:16,594 - __main__ - INFO - ============================================================
2025-07-06 20:11:16,594 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-06 20:11:16,596 - __main__ - INFO - 开始联盟生成阶段...
2025-07-06 20:11:16,596 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-06 20:11:16,596 - __main__ - INFO - 总智能体数: 7
2025-07-06 20:11:16,596 - __main__ - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-06 20:11:16,596 - __main__ - INFO - 交易智能体: TRA
2025-07-06 20:11:16,597 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-06 20:11:16,597 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-06 20:11:16,598 - __main__ - INFO - 联盟剪枝完成:
2025-07-06 20:11:16,599 - __main__ - INFO -   - 总联盟数: 128
2025-07-06 20:11:16,600 - __main__ - INFO -   - 有效联盟: 56
2025-07-06 20:11:16,601 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-06 20:11:16,601 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-06 20:11:16,601 - __main__ - INFO -   - 生成耗时: 0.002s
2025-07-06 20:11:16,601 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-06 20:11:16,602 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-06 20:11:16,602 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-06 20:11:16,602 - __main__ - INFO - 开始交易模拟阶段...
2025-07-06 20:11:16,603 - __main__ - INFO - 限制模拟联盟数量: 56 -> 20
2025-07-06 20:11:16,603 - __main__ - INFO - 启用并发模拟：20 个联盟，最大并发数：30
2025-07-06 20:11:16,604 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:16,604 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,605 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:16,605 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,605 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,609 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,610 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,612 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:16,613 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,617 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:16,618 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:16,619 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,621 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:16,621 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,628 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:16,633 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:16,634 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,635 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,635 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,639 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:16,643 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,645 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,648 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:16,650 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,650 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:16,652 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,654 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,660 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:16,669 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,670 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:16,670 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:16,671 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-06 20:11:16,673 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:16,674 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,689 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,701 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:16,710 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:16,711 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:16,732 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-06 20:11:16,738 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,052 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_201114\\prompts\\BeOA\\opt_BeOA_20250706_154226_6027c255.json'
2025-07-06 20:11:17,064 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,066 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,067 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:17,067 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,068 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,068 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,068 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,069 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,069 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TRA
2025-07-06 20:11:17,071 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TRA
2025-07-06 20:11:17,087 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,089 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,089 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TRA
2025-07-06 20:11:17,090 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:17,091 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (0.49s)
2025-07-06 20:11:17,091 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,093 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,093 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,093 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,093 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,094 - __main__ - WARNING - 联盟目录不存在，创建: FAA_TAA_TRA
2025-07-06 20:11:17,105 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,113 - contribution_assessment.assessor.ContributionAssessor - ERROR - 创建数据备份失败: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'data/backups\\backup_20250706_201114\\prompts\\FAA\\opt_FAA_20250705_232026_85332e41.json'
2025-07-06 20:11:17,114 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,118 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,118 - __main__ - INFO - 创建联盟存储: FAA_TAA_TRA
2025-07-06 20:11:17,119 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,119 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,119 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,119 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:17,120 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,120 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,120 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,120 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,121 - __main__ - INFO - 保存联盟模拟结果: FAA_TAA_TRA
2025-07-06 20:11:17,121 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,122 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,123 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 0.0000 (0.51s)
2025-07-06 20:11:17,123 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,123 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,125 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:17,125 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,126 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_NOA_TRA
2025-07-06 20:11:17,140 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,149 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,150 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,153 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,158 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,159 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:17,160 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,160 - __main__ - INFO - 创建联盟存储: FAA_NAA_NOA_TRA
2025-07-06 20:11:17,160 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,161 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,166 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,172 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,176 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,178 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:17,178 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,179 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,179 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_NOA_TRA
2025-07-06 20:11:17,186 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,191 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,192 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.59s)
2025-07-06 20:11:17,192 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,194 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,202 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.59s)
2025-07-06 20:11:17,203 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:17,204 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,219 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,225 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,240 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,254 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,254 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,257 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,257 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,268 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,269 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,269 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,269 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,270 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,270 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,270 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,271 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,271 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:17,272 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,272 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,272 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,273 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,273 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,274 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,274 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,274 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,275 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,275 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,276 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,276 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,277 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.67s)
2025-07-06 20:11:17,277 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,278 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,278 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,279 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,280 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,280 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,282 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,283 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,284 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,284 - __main__ - INFO - 创建联盟存储: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,284 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,284 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,286 - __main__ - WARNING - 联盟目录不存在，创建: NOA_TAA_TRA
2025-07-06 20:11:17,286 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,286 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,286 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,287 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,287 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,289 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:17,290 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,290 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,291 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,291 - __main__ - INFO - 保存联盟模拟结果: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,291 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,291 - __main__ - INFO - 创建联盟存储: NOA_TAA_TRA
2025-07-06 20:11:17,292 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,293 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,293 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,295 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.67s)
2025-07-06 20:11:17,295 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:17,297 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:17,298 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_TAA_TRA
2025-07-06 20:11:17,303 - __main__ - INFO - 保存联盟模拟结果: NOA_TAA_TRA
2025-07-06 20:11:17,304 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (0.68s)
2025-07-06 20:11:17,306 - __main__ - INFO - 创建联盟存储: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,308 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:17,309 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,309 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:17,311 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:17,312 - __main__ - INFO - 创建联盟存储: BeOA_TAA_TRA
2025-07-06 20:11:17,314 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:17,316 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,319 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (0.68s)
2025-07-06 20:11:17,320 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:17,322 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,322 - __main__ - INFO - 📊 并发进度: 10/20 (50.0%)
2025-07-06 20:11:17,322 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.71s)
2025-07-06 20:11:17,323 - __main__ - INFO - 保存联盟模拟结果: BeOA_TAA_TRA
2025-07-06 20:11:17,325 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.69s)
2025-07-06 20:11:17,339 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,340 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,341 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,341 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,341 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,341 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,348 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,351 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,362 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,363 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,370 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,370 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,371 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,371 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,371 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:17,378 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,384 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,386 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,386 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,394 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,418 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,434 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,436 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,438 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,449 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,461 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:17,462 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,464 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,464 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,465 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,465 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,465 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,466 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,466 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,466 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,466 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:17,466 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,466 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,466 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,466 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,467 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-06 20:11:17,467 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,467 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,467 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,467 - __main__ - INFO - 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,467 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.82s)
2025-07-06 20:11:17,467 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,467 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,468 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:17,468 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_TRA
2025-07-06 20:11:17,468 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,468 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,468 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_TAA_TRA
2025-07-06 20:11:17,468 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,468 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,469 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:17,469 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,469 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,469 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,469 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,469 - __main__ - INFO - 交易天数: 1
2025-07-06 20:11:17,470 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,470 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,470 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,470 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,470 - __main__ - INFO - 创建联盟存储: BeOA_FAA_TRA
2025-07-06 20:11:17,470 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,471 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,471 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,471 - __main__ - INFO - 创建联盟存储: BOA_NAA_TAA_TRA
2025-07-06 20:11:17,471 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,471 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,471 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:17,472 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_TRA
2025-07-06 20:11:17,473 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_TAA_TRA
2025-07-06 20:11:17,474 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,476 - __main__ - WARNING - 联盟目录不存在，创建: TAA_TRA
2025-07-06 20:11:17,477 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_TRA
2025-07-06 20:11:17,477 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_TAA_TRA
2025-07-06 20:11:17,478 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,479 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.81s)
2025-07-06 20:11:17,481 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 0.0000 (0.82s)
2025-07-06 20:11:17,482 - __main__ - INFO - 创建联盟存储: FAA_NAA_TRA
2025-07-06 20:11:17,483 - __main__ - INFO - 创建联盟存储: BOA_BeOA_TAA_TRA
2025-07-06 20:11:17,483 - __main__ - INFO - 创建联盟存储: TAA_TRA
2025-07-06 20:11:17,485 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,487 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:17,488 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (0.84s)
2025-07-06 20:11:17,489 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_TRA
2025-07-06 20:11:17,489 - __main__ - INFO - 保存联盟模拟结果: TAA_TRA
2025-07-06 20:11:17,490 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.85s)
2025-07-06 20:11:17,491 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_TAA_TRA
2025-07-06 20:11:17,491 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:17,491 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (0.82s)
2025-07-06 20:11:17,492 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (0.85s)
2025-07-06 20:11:17,493 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.82s)
2025-07-06 20:11:17,493 - __main__ - INFO - 📊 并发进度: 20/20 (100.0%)
2025-07-06 20:11:17,496 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 20 个，失败 0 个，总耗时 0.89s
2025-07-06 20:11:17,498 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-06 20:11:17,499 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:17,500 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:17,500 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:17,501 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:17,501 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:17,501 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:17,501 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:17,501 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:17,502 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:17,502 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:17,502 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:17,502 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:17,502 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:17,503 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:17,503 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:17,503 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:17,503 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:17,503 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:17,504 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:17,504 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:17,504 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:17,505 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 20:11:17,505 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:17,505 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-06 20:11:17,505 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-06 20:11:17,505 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 20 个
2025-07-06 20:11:17,505 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-06 20:11:17,505 - __main__ - INFO - ==================================================
2025-07-06 20:11:17,505 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-06 20:11:17,506 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:17,506 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:17,506 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:17,506 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:17,506 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:17,506 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:17,506 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:17,507 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:17,507 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:17,507 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:17,507 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:17,507 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:17,508 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:17,508 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:17,508 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:17,508 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:17,509 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:17,509 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:17,509 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:17,509 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:17,510 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:17,510 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 20:11:17,510 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:17,510 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-06 20:11:17,511 - __main__ - INFO - ==================================================
2025-07-06 20:11:17,511 - __main__ - INFO - 贡献度评估完成，总耗时: 0.92s
2025-07-06 20:11:17,512 - __main__ - INFO - ✅ 每日交易决策执行成功 - 日期: 2025-01-03
2025-07-06 20:11:17,512 - __main__ - INFO - 📈 最佳联盟: N/A
2025-07-06 20:11:17,512 - __main__ - INFO - 📊 夏普比率: N/A
2025-07-06 20:11:17,513 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 20:11:17,514 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 20:11:17,514 - __main__ - INFO - 📊 执行每日交易决策 - 第1周，第6/7天
2025-07-06 20:11:17,515 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-06
2025-07-06 20:11:17,515 - __main__ - INFO - 分析缓存初始化完成
2025-07-06 20:11:17,515 - __main__ - INFO - 联盟管理器初始化完成
2025-07-06 20:11:17,516 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:11:17,516 - __main__ - INFO - 交易模拟器初始化完成
2025-07-06 20:11:17,516 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:11:17,517 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:11:17,517 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:11:17,518 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:11:17,518 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:11:17,518 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:11:17,519 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:11:17,531 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:11:17,534 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:11:17,534 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:11:17,552 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:11:17,553 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:11:17,553 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-06 20:11:17,553 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 禁用)
2025-07-06 20:11:17,553 - __main__ - INFO - 📊 执行智能体联盟交易模拟 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:17,554 - __main__ - WARNING - LLM接口不可用，无法创建LLM智能体
2025-07-06 20:11:17,554 - __main__ - INFO - 开始贡献度评估流程
2025-07-06 20:11:17,554 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:17,554 - __main__ - INFO - 可用智能体实例: 无（将使用模拟智能体）
2025-07-06 20:11:17,559 - __main__ - INFO - 开始新实验: experiment_20250706_201117
2025-07-06 20:11:17,559 - __main__ - INFO - 开始新实验: experiment_20250706_201117
2025-07-06 20:11:17,559 - __main__ - INFO - ==================================================
2025-07-06 20:11:17,560 - __main__ - INFO - 阶段1: 分析缓存
2025-07-06 20:11:17,560 - __main__ - WARNING - LLM接口不可用且无智能体实例，使用模拟数据...
2025-07-06 20:11:17,560 - __main__ - INFO - 开始分析缓存阶段...
2025-07-06 20:11:17,560 - __main__ - WARNING - 未提供分析智能体实例，将使用模拟数据
2025-07-06 20:11:17,560 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-06 20:11:17,560 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-06 20:11:17,561 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-06 20:11:17,561 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-06 20:11:17,561 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-06 20:11:17,561 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-06 20:11:17,561 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-06 20:11:17,561 - __main__ - INFO - ==================================================
2025-07-06 20:11:17,561 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-06 20:11:17,561 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-06 20:11:17,561 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-06 20:11:17,562 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-06 20:11:17,562 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,562 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-06 20:11:17,562 - __main__ - INFO - ============================================================
2025-07-06 20:11:17,562 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-06 20:11:17,563 - __main__ - INFO - 开始联盟生成阶段...
2025-07-06 20:11:17,563 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-06 20:11:17,563 - __main__ - INFO - 总智能体数: 7
2025-07-06 20:11:17,563 - __main__ - INFO - 分析智能体: {'FAA', 'NAA', 'TAA'}
2025-07-06 20:11:17,563 - __main__ - INFO - 交易智能体: TRA
2025-07-06 20:11:17,563 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-06 20:11:17,564 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-06 20:11:17,564 - __main__ - INFO - 联盟剪枝完成:
2025-07-06 20:11:17,564 - __main__ - INFO -   - 总联盟数: 128
2025-07-06 20:11:17,565 - __main__ - INFO -   - 有效联盟: 56
2025-07-06 20:11:17,565 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-06 20:11:17,565 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-06 20:11:17,566 - __main__ - INFO -   - 生成耗时: 0.001s
2025-07-06 20:11:17,566 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-06 20:11:17,567 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-06 20:11:17,567 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-06 20:11:17,567 - __main__ - INFO - 开始交易模拟阶段...
2025-07-06 20:11:17,567 - __main__ - INFO - 限制模拟联盟数量: 56 -> 20
2025-07-06 20:11:17,567 - __main__ - INFO - 启用并发模拟：20 个联盟，最大并发数：30
2025-07-06 20:11:17,568 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:17,568 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:17,568 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,569 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,569 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,570 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,571 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,571 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,580 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:17,583 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:17,585 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,586 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,588 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:17,588 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:17,591 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:17,591 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:17,593 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,593 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,595 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,603 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:17,604 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,613 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:17,614 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:17,618 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,620 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,626 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,631 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,636 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:17,636 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:17,636 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:17,638 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,650 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-06 20:11:17,653 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,654 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:17,654 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,656 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:17,678 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:17,693 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:17,694 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-06 20:11:17,696 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:18,016 - __main__ - INFO - 数据备份完成: backup_20250706_201114 (0.41 MB)
2025-07-06 20:11:18,001 - data.comprehensive_storage_manager.ComprehensiveStorageManager - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_201114\\prompts\\TRA\\opt_TRA_20250705_222038_1f75fab2.json'
2025-07-06 20:11:19,799 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:20,017 - __main__ - INFO - ============================================================
2025-07-06 20:11:20,041 - __main__ - INFO - 数据备份完成: backup_20250706_201115 (0.41 MB)
2025-07-06 20:11:20,500 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-06 20:11:20,901 - __main__ - INFO - 数据备份完成: backup_20250706_201116 (0.41 MB)
2025-07-06 20:11:21,020 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:21,244 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,323 - __main__ - INFO - ============================================================
2025-07-06 20:11:21,653 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,366 - __main__ - INFO - ============================================================
2025-07-06 20:11:21,783 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:22,270 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:22,827 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:22,922 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:23,050 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:23,323 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:23,325 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:23,589 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:23,631 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,572 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,069 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,102 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,121 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,187 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,240 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,297 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:21,600 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,332 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,361 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:21,739 - __main__ - INFO - 数据备份完成: backup_20250706_201117 (0.41 MB)
2025-07-06 20:11:24,386 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:24,402 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,404 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:24,404 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,771 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:24,421 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,435 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,784 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:24,562 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,064 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-06 20:11:24,585 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'TRA'}
2025-07-06 20:11:24,596 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'NOA', 'BeOA'}
2025-07-06 20:11:24,647 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,660 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,679 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,696 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,934 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-06 20:11:24,717 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,722 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,734 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,957 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:24,762 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-06 20:11:24,770 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:24,412 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,774 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,783 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:24,454 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,852 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,880 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'FAA', 'TAA'}
2025-07-06 20:11:24,891 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,899 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,905 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,925 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'}
2025-07-06 20:11:24,926 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:24,929 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'}
2025-07-06 20:11:24,710 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,946 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,950 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'}
2025-07-06 20:11:24,952 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'BeOA'}
2025-07-06 20:11:24,742 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,967 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:24,983 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:24,985 - __main__ - INFO - ============================================================
2025-07-06 20:11:24,990 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'}
2025-07-06 20:11:24,993 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:24,996 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,014 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'}
2025-07-06 20:11:25,026 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,030 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,030 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'NAA', 'TAA', 'BOA'}
2025-07-06 20:11:25,046 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,050 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,063 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,067 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,067 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,071 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'NAA', 'NOA', 'TRA'}
2025-07-06 20:11:25,079 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,081 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,084 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,101 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,103 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,111 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,112 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,202 - __main__ - WARNING - 联盟目录不存在，创建: NOA_TAA_TRA
2025-07-06 20:11:25,118 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,120 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,127 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,130 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,133 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,138 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,144 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,251 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,146 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,146 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,154 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,154 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,164 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,165 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,166 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,181 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,285 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,200 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,113 - __main__ - INFO - 周总收益率: 0.0000
2025-07-06 20:11:25,204 - __main__ - INFO - 创建联盟存储: NOA_TAA_TRA
2025-07-06 20:11:25,211 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,232 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,238 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,243 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,243 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,333 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,145 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,253 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,254 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,254 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,254 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,254 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,261 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,269 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,284 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,182 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,293 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,299 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,309 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,311 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,311 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,314 - __main__ - INFO - 保存联盟模拟结果: NOA_TAA_TRA
2025-07-06 20:11:25,316 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,333 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,250 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-06 20:11:25,436 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,353 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,366 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_TRA
2025-07-06 20:11:25,376 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,380 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,380 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,381 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,384 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,392 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,399 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,470 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,411 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,416 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,427 - __main__ - INFO - 交易天数: 2
2025-07-06 20:11:25,430 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,433 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,435 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (7.85s)
2025-07-06 20:11:25,435 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,436 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,334 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,436 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,437 - __main__ - INFO - 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,438 - __main__ - INFO - 创建联盟存储: FAA_NAA_TRA
2025-07-06 20:11:25,443 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,520 - __main__ - WARNING - 联盟目录不存在，创建: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:25,454 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,463 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,467 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_TAA_TRA
2025-07-06 20:11:25,469 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,407 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:25,483 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_TAA_TRA
2025-07-06 20:11:25,485 - __main__ - WARNING - 联盟目录不存在，创建: TAA_TRA
2025-07-06 20:11:25,486 - __main__ - INFO - 创建联盟存储: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,487 - __main__ - INFO - ============================================================
2025-07-06 20:11:25,491 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,493 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_TRA
2025-07-06 20:11:25,498 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,500 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,502 - __main__ - WARNING - 联盟目录不存在，创建: FAA_TAA_TRA
2025-07-06 20:11:25,516 - __main__ - INFO - 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,516 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TRA
2025-07-06 20:11:25,519 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_TRA
2025-07-06 20:11:25,450 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,521 - __main__ - WARNING - 联盟目录不存在，创建: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,524 - __main__ - INFO - 创建联盟存储: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:25,531 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,536 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:25,536 - __main__ - INFO - 创建联盟存储: BOA_BeOA_TAA_TRA
2025-07-06 20:11:25,539 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:25,547 - __main__ - INFO - 创建联盟存储: BeOA_TAA_TRA
2025-07-06 20:11:25,550 - __main__ - INFO - 创建联盟存储: TAA_TRA
2025-07-06 20:11:25,551 - __main__ - INFO - 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 1
2025-07-06 20:11:25,553 - __main__ - INFO - 保存联盟模拟结果: BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,554 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,556 - __main__ - INFO - 创建联盟存储: BeOA_FAA_TRA
2025-07-06 20:11:25,557 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,568 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,571 - __main__ - INFO - 创建联盟存储: FAA_TAA_TRA
2025-07-06 20:11:25,571 - __main__ - WARNING - 联盟目录不存在，创建: BOA_NAA_TAA_TRA
2025-07-06 20:11:25,581 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TRA
2025-07-06 20:11:25,582 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (7.97s)
2025-07-06 20:11:25,583 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,586 - __main__ - INFO - 创建联盟存储: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,587 - __main__ - INFO - 保存联盟模拟结果: BOA_FAA_NAA_TAA_TRA
2025-07-06 20:11:25,593 - __main__ - WARNING - 联盟目录不存在，创建: FAA_NAA_NOA_TRA
2025-07-06 20:11:25,599 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:25,604 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_TAA_TRA
2025-07-06 20:11:25,609 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_TAA_TRA
2025-07-06 20:11:25,617 - __main__ - INFO - 保存联盟模拟结果: BeOA_TAA_TRA
2025-07-06 20:11:25,619 - __main__ - INFO - 保存联盟模拟结果: TAA_TRA
2025-07-06 20:11:25,621 - __main__ - WARNING - 联盟目录不存在，创建: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,622 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (8.04s)
2025-07-06 20:11:25,622 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (8.00s)
2025-07-06 20:11:25,634 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_TRA
2025-07-06 20:11:25,643 - __main__ - INFO - 创建联盟存储: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,645 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (8.01s)
2025-07-06 20:11:25,652 - __main__ - INFO - 创建联盟存储: BOA_NAA_TAA_TRA
2025-07-06 20:11:25,652 - __main__ - INFO - 保存联盟模拟结果: FAA_TAA_TRA
2025-07-06 20:11:25,654 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TRA
2025-07-06 20:11:25,658 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (8.07s)
2025-07-06 20:11:25,661 - __main__ - INFO - 创建联盟存储: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,661 - __main__ - INFO - 保存联盟模拟结果: BeOA_FAA_NOA_TAA_TRA
2025-07-06 20:11:25,667 - __main__ - INFO - 创建联盟存储: FAA_NAA_NOA_TRA
2025-07-06 20:11:25,669 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (8.05s)
2025-07-06 20:11:25,670 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (8.07s)
2025-07-06 20:11:25,671 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TRA
2025-07-06 20:11:25,671 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (8.08s)
2025-07-06 20:11:25,672 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (8.02s)
2025-07-06 20:11:25,683 - __main__ - INFO - 创建联盟存储: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,686 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (8.03s)
2025-07-06 20:11:25,688 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,689 - __main__ - INFO - 保存联盟模拟结果: BOA_NAA_TAA_TRA
2025-07-06 20:11:25,769 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NAA', 'TAA', 'BOA'} 模拟完成: 0.0000 (8.13s)
2025-07-06 20:11:25,697 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (8.13s)
2025-07-06 20:11:25,699 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (8.07s)
2025-07-06 20:11:25,700 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_FAA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,702 - __main__ - INFO - 保存联盟模拟结果: FAA_NAA_NOA_TRA
2025-07-06 20:11:25,730 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (8.16s)
2025-07-06 20:11:25,746 - __main__ - INFO - 📊 并发进度: 10/20 (50.0%)
2025-07-06 20:11:25,753 - __main__ - INFO - 保存联盟模拟结果: BOA_BeOA_NAA_NOA_TAA_TRA
2025-07-06 20:11:25,769 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA'} 模拟完成: 0.0000 (8.18s)
2025-07-06 20:11:25,694 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA', 'TAA'} 模拟完成: 0.0000 (8.11s)
2025-07-06 20:11:25,781 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (8.21s)
2025-07-06 20:11:25,782 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (8.20s)
2025-07-06 20:11:25,821 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'BOA', 'NOA', 'TAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (8.25s)
2025-07-06 20:11:25,938 - __main__ - INFO - 📊 并发进度: 20/20 (100.0%)
2025-07-06 20:11:25,950 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 20 个，失败 0 个，总耗时 8.38s
2025-07-06 20:11:25,966 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-06 20:11:25,969 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:25,985 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:25,996 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:25,997 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:25,998 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:25,999 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:25,999 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:26,000 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:26,002 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:26,002 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:26,002 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:26,002 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:26,003 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:26,003 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:26,003 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:26,004 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:26,005 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:26,005 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:26,005 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:26,047 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:26,062 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:26,068 - __main__ - INFO - Shapley值计算完成，耗时 0.076s
2025-07-06 20:11:26,069 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:26,070 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-06 20:11:26,074 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-06 20:11:26,076 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 20 个
2025-07-06 20:11:26,082 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-06 20:11:26,097 - __main__ - INFO - ==================================================
2025-07-06 20:11:26,104 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-06 20:11:26,131 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-06 20:11:26,138 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:26,138 - __main__ - INFO - 已提供 20 个联盟的特征函数值
2025-07-06 20:11:26,154 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:26,163 - __main__ - INFO - 联盟值补全完成: 已提供 20 个，补全 108 个
2025-07-06 20:11:26,166 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:26,167 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:26,168 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 20:11:26,168 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:26,169 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 20:11:26,169 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:26,170 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 20:11:26,170 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:26,170 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 20:11:26,171 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:26,171 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 20:11:26,172 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:26,176 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 20:11:26,180 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:26,183 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 20:11:26,183 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 20:11:26,184 - __main__ - INFO - Shapley值计算完成，耗时 0.045s
2025-07-06 20:11:26,185 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-06 20:11:26,185 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-06 20:11:26,185 - __main__ - INFO - ==================================================
2025-07-06 20:11:26,185 - __main__ - INFO - 贡献度评估完成，总耗时: 8.63s
2025-07-06 20:11:26,186 - __main__ - INFO - ✅ 每日交易决策执行成功 - 日期: 2025-01-06
2025-07-06 20:11:26,187 - __main__ - INFO - 📈 最佳联盟: N/A
2025-07-06 20:11:26,187 - __main__ - INFO - 📊 夏普比率: N/A
2025-07-06 20:11:26,190 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 20:11:26,193 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 20:11:26,196 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 20:11:26,197 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:11:26,197 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 20:11:26,202 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 20:11:26,203 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 20:11:26,204 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 20:11:26,204 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 20:11:26,207 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 20:11:26,211 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 20:11:26,212 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 20:11:26,213 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.025554
2025-07-06 20:11:26,214 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 20:11:26,215 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.024801
2025-07-06 20:11:26,216 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 20:11:26,216 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.045297
2025-07-06 20:11:26,217 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 20:11:26,217 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.014532
2025-07-06 20:11:26,218 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 20:11:26,218 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.005595
2025-07-06 20:11:26,218 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 20:11:26,218 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.031262
2025-07-06 20:11:26,219 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 20:11:26,219 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.058036
2025-07-06 20:11:26,219 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.205076 = 大联盟值 0.205076
2025-07-06 20:11:26,220 - __main__ - INFO - Shapley值计算完成，耗时 0.016s
2025-07-06 20:11:26,221 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 20:11:26,221 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 20:11:26,227 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 20:11:26,231 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'BOA']
2025-07-06 20:11:26,232 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 20:11:26,232 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 20:11:26,234 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 20:11:26,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:26,237 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:26,238 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 20:11:26,250 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018EC5E0E750>
2025-07-06 20:11:26,252 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 20:11:26,252 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:26,252 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 20:11:26,252 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:26,253 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 20:11:26,254 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 20:11:26,254 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000018EC5E45150> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 20:11:26,430 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018EC5743FE0>
2025-07-06 20:11:26,431 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:26,431 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:26,432 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:26,432 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:26,433 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:31,067 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117518038871616206e00833b1f24d784f0319a347af4813801d48b;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201127b01d0d3dd87d4faf'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:31,069 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:31,070 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:31,071 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:31,072 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:31,073 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:31,074 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:31,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:31,077 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:31,079 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:31,080 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:31,081 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:31,083 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:31,083 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:35,389 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070620113134f1fe81987540ae'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:35,390 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:35,390 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:35,391 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:35,391 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:35,392 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:35,392 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:35,394 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:35,394 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:35,396 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:35,396 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:35,396 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:35,397 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:35,397 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:38,611 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070620113675d9239e553f409e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:38,611 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:38,611 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:38,611 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:38,611 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:38,611 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:38,612 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:38,612 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:38,613 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:38,614 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:38,614 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:38,614 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:38,614 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:38,614 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:41,381 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201139dbc615207ae5473e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:41,382 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:41,383 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:41,384 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:41,384 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:41,384 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:41,384 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:41,385 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:41,385 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:41,386 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:41,386 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:41,386 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:41,386 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:41,387 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:44,426 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201142ad325df8bffa4173'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:44,427 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:44,427 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:44,427 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:44,427 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:44,428 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:44,428 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:44,429 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:44,429 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:44,429 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:44,430 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:44,430 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:44,430 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:44,430 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:47,200 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062011455b1c2ebce5724e17'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:47,201 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:47,202 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:47,202 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:47,202 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:47,203 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:47,203 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:47,204 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:47,204 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:47,205 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:47,206 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:47,206 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:47,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:47,207 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:51,528 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201147b5157707d4f340e2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:51,528 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:51,528 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:51,528 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:51,528 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:51,528 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:51,528 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:51,529 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:51,530 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:51,530 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:51,531 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:51,531 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:51,531 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:51,531 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:54,228 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201152be7b429721cb4555'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:54,228 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:54,229 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:54,230 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:54,230 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:54,230 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:54,231 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:54,233 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 20:11:54,246 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_201154_1d02f9a5
2025-07-06 20:11:54,247 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_201154_1d02f9a5
2025-07-06 20:11:54,247 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_201154_1d02f9a5
2025-07-06 20:11:54,247 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.718756
2025-07-06 20:11:54,247 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 20:11:54,248 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 20:11:54,248 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 20:11:54,248 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:54,249 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:54,249 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:54,250 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:54,250 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:54,250 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:54,250 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:56,802 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:11:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201154a67839a720f74edc'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:56,803 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:56,804 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:56,804 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:56,806 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:56,806 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:56,807 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:56,809 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:56,809 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:56,810 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:56,811 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:56,811 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:56,812 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:56,812 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:11:59,813 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201157bcd0865171a14f11'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:11:59,814 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:11:59,814 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:11:59,814 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:11:59,814 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:11:59,814 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:11:59,814 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:11:59,815 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:11:59,815 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:11:59,816 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:11:59,816 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:11:59,816 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:11:59,816 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:11:59,817 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:02,794 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062012001d28b377a2af481c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:02,794 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:02,794 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:02,796 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:02,797 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:02,797 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:02,797 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:02,799 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:12:02,799 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:12:02,800 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:12:02,801 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:12:02,801 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:12:02,802 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:12:02,802 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:07,380 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:08 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201203b504cfab341045fb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:07,381 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:07,381 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:07,381 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:07,381 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:07,382 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:07,382 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:07,383 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:12:07,383 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:12:07,384 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:12:07,384 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:12:07,385 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:12:07,385 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:12:07,385 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:10,722 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201208e846092b4db748f5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:10,723 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:10,723 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:10,724 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:10,724 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:10,724 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:10,724 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:10,728 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:12:10,728 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:12:10,730 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:12:10,730 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:12:10,731 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:12:10,731 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:12:10,731 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:13,097 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201211f198d482c7c249e7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:13,097 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:13,097 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:13,097 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:13,097 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:13,097 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:13,097 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:13,099 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:12:13,099 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:12:13,100 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:12:13,100 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:12:13,101 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:12:13,101 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:12:13,101 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:15,649 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062012133841cc70d7b64523'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:15,650 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:15,650 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:15,651 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:15,651 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:15,651 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:15,651 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:15,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 20:12:15,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 20:12:15,655 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 20:12:15,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 20:12:15,655 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 20:12:15,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 20:12:15,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 20:12:18,485 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 12:12:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706201216f5bac323b4b54b0b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 20:12:18,486 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 20:12:18,486 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 20:12:18,487 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 20:12:18,488 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 20:12:18,488 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 20:12:18,488 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 20:12:18,490 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 20:12:18,499 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_201218_28e4d0b8
2025-07-06 20:12:18,499 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_201218_28e4d0b8
2025-07-06 20:12:18,499 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_201218_28e4d0b8
2025-07-06 20:12:18,500 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.823405
2025-07-06 20:12:18,500 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 20:12:18,516 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 20:12:18,516 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 20:12:18,516 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 20:12:18,516 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 20:12:18,516 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 20:12:18,516 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 20:12:18,516 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 20:12:18,517 - __main__ - INFO -   - 总周期数: 1
2025-07-06 20:12:18,517 - __main__ - INFO -   - 成功周期: 1
2025-07-06 20:12:18,517 - __main__ - INFO -   - 失败周期: 0
2025-07-06 20:12:18,517 - __main__ - INFO -   - 成功率: 100.00%
2025-07-06 20:12:18,517 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 20:12:18,517 - __main__ - INFO - ====================================================================================================
2025-07-06 20:12:18,517 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 20:12:18,517 - __main__ - INFO - 📊 总周期数: 1
2025-07-06 20:12:18,517 - __main__ - INFO - 📊 总交易天数: 6
2025-07-06 20:12:18,517 - __main__ - INFO - ⏱️  总执行时间: 63.90秒
2025-07-06 20:12:18,518 - __main__ - INFO - ====================================================================================================
2025-07-06 20:12:18,518 - __main__ - INFO - 开始交易会话: assessment_20250706_201218
2025-07-06 20:12:18,518 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 20:12:18,521 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_201218
2025-07-06 20:12:18,521 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_201218 (系统盈亏: 0.00)
2025-07-06 20:12:18,521 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_201218
2025-07-06 20:12:18,521 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 20:12:18,521 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 20:12:18,522 - __main__ - INFO - ====================================================================================================
2025-07-06 20:12:18,522 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 20:12:18,522 - __main__ - INFO - ====================================================================================================
