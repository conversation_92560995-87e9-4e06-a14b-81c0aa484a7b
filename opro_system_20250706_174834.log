2025-07-06 17:48:34,045 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,046 - __main__ - INFO - OPRO系统启动
2025-07-06 17:48:34,046 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,046 - __main__ - INFO - 运行模式: enhanced_dashboard
2025-07-06 17:48:34,046 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 17:48:34,046 - __main__ - INFO - OPRO启用: True
2025-07-06 17:48:34,046 - __main__ - INFO - 数据存储启用: True
2025-07-06 17:48:34,046 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 17:48:34,047 - __main__ - INFO - 初始化系统...
2025-07-06 17:48:34,047 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 17:48:34,048 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 17:48:34,048 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 17:48:34,054 - __main__ - INFO - 数据库初始化完成
2025-07-06 17:48:34,055 - __main__ - INFO - 自动备份线程已启动
2025-07-06 17:48:34,055 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:48:34,055 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 17:48:34,055 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 17:48:34,062 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:48:34,062 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:48:34,062 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 17:48:34,070 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:48:34,070 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:48:34,070 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 17:48:34,071 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 17:48:34,071 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 17:48:34,071 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 17:48:34,071 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 17:48:34,071 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 17:48:34,079 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,079 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:48:34,079 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:48:34,080 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,080 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 17:48:34,080 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 17:48:34,080 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 17:48:34,080 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 17:48:34,081 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 17:48:34,081 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:48:34,082 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 17:48:34,082 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:48:34,091 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:48:34,092 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:48:34,092 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 17:48:34,460 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 17:48:34,460 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:48:34,558 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 17:48:34,559 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 17:48:34,559 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 17:48:34,559 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 17:48:34,559 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 17:48:34,560 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:48:34,565 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:48:34,566 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:48:34,567 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:48:34,567 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:48:34,568 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:48:34,568 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:48:34,568 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:48:34,568 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,570 - __main__ - INFO - 加载历史数据完成: 6 个实验记录
2025-07-06 17:48:34,570 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:48:34,570 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:48:34,570 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:48:34,570 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 17:48:34,571 - __main__ - INFO - 系统初始化完成
2025-07-06 17:48:34,571 - __main__ - INFO - ================================================================================
2025-07-06 17:48:34,571 - __main__ - INFO - 🚀 运行模式: 增强仪表板模式（完整OPRO交易系统）
2025-07-06 17:48:34,571 - __main__ - INFO - ================================================================================
2025-07-06 17:48:34,571 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-04-01
2025-07-06 17:48:34,571 - __main__ - INFO - 🤖 目标智能体: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,572 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,572 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:48:34,572 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:48:34,572 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,573 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:48:34,584 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:48:34,586 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:48:34,587 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:48:34,588 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:48:34,588 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:48:34,589 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:48:34,589 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:48:34,589 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:48:34,594 - __main__ - INFO - 加载历史数据完成: 6 个实验记录
2025-07-06 17:48:34,594 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:48:34,596 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:48:34,596 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:48:34,597 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,597 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 17:48:34,597 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,598 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-04-01
2025-07-06 17:48:34,598 - __main__ - INFO - 🤖 目标智能体: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,598 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 17:48:34,604 - __main__ - INFO - 存储目录结构初始化完成
2025-07-06 17:48:34,604 - __main__ - INFO - 数据库初始化完成
2025-07-06 17:48:34,605 - __main__ - INFO - 周期性交易存储管理器初始化完成，数据目录: data\trading
2025-07-06 17:48:34,605 - __main__ - INFO - OPRO交易数据集成器初始化完成
2025-07-06 17:48:34,605 - __main__ - INFO - ✅ 交易数据存储集成器初始化完成
2025-07-06 17:48:34,606 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 17:48:34,608 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-04-01
2025-07-06 17:48:34,609 - __main__ - INFO - 📊 总交易日数: 65
2025-07-06 17:48:34,609 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 17:48:34,609 - __main__ - INFO - 🗓️  最后交易日: 2025-04-01
2025-07-06 17:48:34,609 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 17:48:34,610 - __main__ - INFO - 📊 生成了 9 个7天周期
2025-07-06 17:48:34,610 - __main__ - INFO -    - 基线运行周: 5 个
2025-07-06 17:48:34,610 - __main__ - INFO -    - A/B测试周: 4 个
2025-07-06 17:48:34,611 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 17:48:34,611 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 17:48:34,619 - __main__ - INFO - 开始周期记录: week_1_baseline_operation_20250706_174834
2025-07-06 17:48:34,619 - __main__ - INFO - 开始OPRO周期集成: week_1_baseline_operation_20250706_174834
2025-07-06 17:48:34,620 - __main__ - INFO - 📊 开始第 1 周数据存储集成: week_1_baseline_operation_20250706_174834
2025-07-06 17:48:34,620 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 17:48:34,620 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 17:48:34,621 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 17:48:34,621 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 17:48:34,622 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 17:48:34,622 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 17:48:34,622 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 17:48:34,622 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 17:48:34,622 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 17:48:34,624 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 17:48:34,624 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 17:48:34,624 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,624 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,625 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,625 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,626 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,626 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,626 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,627 - __main__ - INFO - Shapley值计算完成，耗时 0.000s
2025-07-06 17:48:34,627 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,628 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,628 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,629 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,629 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 17:48:34,630 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,630 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,630 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,630 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 17:48:34,630 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 17:48:34,630 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 17:48:34,631 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 17:48:34,635 - __main__ - INFO - 记录周期性能指标: 第1周 - baseline
2025-07-06 17:48:34,635 - __main__ - INFO - 📊 记录第 1 周性能指标
2025-07-06 17:48:34,635 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 17:48:34,642 - __main__ - INFO - 创建周期备份: data\trading\backups\week_1_baseline_operation_20250706_174834
2025-07-06 17:48:34,642 - __main__ - INFO - 完成周期记录: 第1周 - baseline_operation
2025-07-06 17:48:34,642 - __main__ - INFO - 完成OPRO周期集成: 第1周
2025-07-06 17:48:34,643 - __main__ - INFO - 📊 完成第 1 周数据存储集成
2025-07-06 17:48:34,643 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 17:48:34,649 - __main__ - INFO - 开始周期记录: week_2_ab_testing_20250706_174834
2025-07-06 17:48:34,650 - __main__ - INFO - 开始OPRO周期集成: week_2_ab_testing_20250706_174834
2025-07-06 17:48:34,650 - __main__ - INFO - 📊 开始第 2 周数据存储集成: week_2_ab_testing_20250706_174834
2025-07-06 17:48:34,651 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 17:48:34,651 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 17:48:34,651 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 17:48:34,652 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 17:48:34,652 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 17:48:34,652 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 17:48:34,652 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 17:48:34,652 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 17:48:34,652 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:48:34,652 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:48:34,652 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:48:34,654 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:48:34,654 - __main__ - INFO - ✅ 智能体 NSA 配置更新成功: optimized
2025-07-06 17:48:34,654 - __main__ - INFO - 🔄 智能体配置更新完成: 成功1, 失败0
2025-07-06 17:48:34,654 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:48:34,654 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_174834
2025-07-06 17:48:34,655 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_174834 - 0 条记录
2025-07-06 17:48:34,655 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_174834
2025-07-06 17:48:34,656 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:48:34,656 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 17:48:34,656 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-16
2025-07-06 17:48:34,656 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 17:48:34,657 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 17:48:34,657 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第2/7天
2025-07-06 17:48:34,657 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 17:48:34,657 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 17:48:34,662 - __main__ - INFO - 记录周期性能指标: 第2周 - optimized
2025-07-06 17:48:34,663 - __main__ - INFO - 📊 记录第 2 周性能指标
2025-07-06 17:48:34,663 - __main__ - INFO - ✅ 第 2 周完成: ab_testing_complete
2025-07-06 17:48:34,664 - __main__ - INFO - 🔬 第 2 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:48:34,664 - __main__ - WARNING - ⚠️  第 2 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:48:34,664 - __main__ - WARNING - 第2周没有A/B测试对比数据
2025-07-06 17:48:34,664 - __main__ - WARNING - ⚠️ 存储系统中也没有第 2 周的获胜实验数据
2025-07-06 17:48:34,676 - __main__ - INFO - 创建周期备份: data\trading\backups\week_2_ab_testing_20250706_174834
2025-07-06 17:48:34,677 - __main__ - INFO - 完成周期记录: 第2周 - ab_testing
2025-07-06 17:48:34,677 - __main__ - INFO - 完成OPRO周期集成: 第2周
2025-07-06 17:48:34,677 - __main__ - INFO - 📊 完成第 2 周数据存储集成
2025-07-06 17:48:34,678 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 17:48:34,682 - __main__ - INFO - 开始周期记录: week_3_baseline_operation_20250706_174834
2025-07-06 17:48:34,682 - __main__ - INFO - 开始OPRO周期集成: week_3_baseline_operation_20250706_174834
2025-07-06 17:48:34,683 - __main__ - INFO - 📊 开始第 3 周数据存储集成: week_3_baseline_operation_20250706_174834
2025-07-06 17:48:34,683 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 17:48:34,683 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 17:48:34,684 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 17:48:34,684 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 17:48:34,684 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,684 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,684 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,685 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,685 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,685 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,686 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,686 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-06 17:48:34,686 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,687 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,687 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,687 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,687 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-16
2025-07-06 17:48:34,688 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,688 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,688 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,688 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 17:48:34,689 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 17:48:34,689 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-24
2025-07-06 17:48:34,689 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 17:48:34,689 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 17:48:34,690 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第4/7天
2025-07-06 17:48:34,690 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 17:48:34,690 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第5/7天
2025-07-06 17:48:34,690 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 17:48:34,691 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第6/7天
2025-07-06 17:48:34,696 - __main__ - INFO - 记录周期性能指标: 第3周 - baseline
2025-07-06 17:48:34,697 - __main__ - INFO - 📊 记录第 3 周性能指标
2025-07-06 17:48:34,697 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 17:48:34,703 - __main__ - INFO - 创建周期备份: data\trading\backups\week_3_baseline_operation_20250706_174834
2025-07-06 17:48:34,703 - __main__ - INFO - 完成周期记录: 第3周 - baseline_operation
2025-07-06 17:48:34,704 - __main__ - INFO - 完成OPRO周期集成: 第3周
2025-07-06 17:48:34,704 - __main__ - INFO - 📊 完成第 3 周数据存储集成
2025-07-06 17:48:34,704 - __main__ - INFO - 🔄 开始第 4 周（ab_testing）: 2025-01-30 到 2025-02-07
2025-07-06 17:48:34,709 - __main__ - INFO - 开始周期记录: week_4_ab_testing_20250706_174834
2025-07-06 17:48:34,710 - __main__ - INFO - 开始OPRO周期集成: week_4_ab_testing_20250706_174834
2025-07-06 17:48:34,710 - __main__ - INFO - 📊 开始第 4 周数据存储集成: week_4_ab_testing_20250706_174834
2025-07-06 17:48:34,710 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-30
2025-07-06 17:48:34,710 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第7/7天
2025-07-06 17:48:34,710 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:48:34,710 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:48:34,710 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:48:34,710 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:48:34,711 - __main__ - INFO - ✅ 智能体 NSA 配置更新成功: optimized
2025-07-06 17:48:34,711 - __main__ - INFO - 🔄 智能体配置更新完成: 成功1, 失败0
2025-07-06 17:48:34,711 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:48:34,711 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_174834
2025-07-06 17:48:34,711 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_174834 - 0 条记录
2025-07-06 17:48:34,712 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_174834
2025-07-06 17:48:34,712 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:48:34,712 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-31
2025-07-06 17:48:34,712 - __main__ - INFO - 🆕 开始第5周 - 基线运行阶段: 2025-01-31
2025-07-06 17:48:34,712 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第1/7天
2025-07-06 17:48:34,712 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-03
2025-07-06 17:48:34,712 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第4/7天
2025-07-06 17:48:34,713 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-04
2025-07-06 17:48:34,713 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第5/7天
2025-07-06 17:48:34,713 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-05
2025-07-06 17:48:34,713 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第6/7天
2025-07-06 17:48:34,714 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-06
2025-07-06 17:48:34,714 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第7/7天
2025-07-06 17:48:34,714 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,714 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,714 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,714 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,714 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,714 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,714 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,715 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-06 17:48:34,715 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,715 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,716 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,716 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,716 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-31
2025-07-06 17:48:34,716 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,716 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,717 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,717 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-07
2025-07-06 17:48:34,722 - __main__ - INFO - 记录周期性能指标: 第4周 - optimized
2025-07-06 17:48:34,724 - __main__ - INFO - 📊 记录第 4 周性能指标
2025-07-06 17:48:34,724 - __main__ - INFO - ✅ 第 4 周完成: baseline_complete
2025-07-06 17:48:34,731 - __main__ - INFO - 创建周期备份: data\trading\backups\week_4_ab_testing_20250706_174834
2025-07-06 17:48:34,732 - __main__ - INFO - 完成周期记录: 第4周 - ab_testing
2025-07-06 17:48:34,732 - __main__ - INFO - 完成OPRO周期集成: 第4周
2025-07-06 17:48:34,732 - __main__ - INFO - 📊 完成第 4 周数据存储集成
2025-07-06 17:48:34,732 - __main__ - INFO - 🔄 开始第 5 周（baseline_operation）: 2025-02-10 到 2025-02-18
2025-07-06 17:48:34,735 - __main__ - INFO - 开始周期记录: week_5_baseline_operation_20250706_174834
2025-07-06 17:48:34,736 - __main__ - INFO - 开始OPRO周期集成: week_5_baseline_operation_20250706_174834
2025-07-06 17:48:34,736 - __main__ - INFO - 📊 开始第 5 周数据存储集成: week_5_baseline_operation_20250706_174834
2025-07-06 17:48:34,736 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-10
2025-07-06 17:48:34,737 - __main__ - INFO - 🆕 开始第6周 - A/B测试阶段: 2025-02-10
2025-07-06 17:48:34,737 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第1/7天
2025-07-06 17:48:34,737 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-11
2025-07-06 17:48:34,737 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第2/7天
2025-07-06 17:48:34,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-12
2025-07-06 17:48:34,738 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第3/7天
2025-07-06 17:48:34,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-13
2025-07-06 17:48:34,738 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第4/7天
2025-07-06 17:48:34,738 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-14
2025-07-06 17:48:34,739 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第5/7天
2025-07-06 17:48:34,739 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-17
2025-07-06 17:48:34,739 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-18
2025-07-06 17:48:34,739 - __main__ - INFO - 🆕 开始第7周 - 基线运行阶段: 2025-02-18
2025-07-06 17:48:34,740 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第1/7天
2025-07-06 17:48:34,740 - __main__ - INFO - ✅ 第 5 周完成: in_progress
2025-07-06 17:48:34,746 - __main__ - INFO - 创建周期备份: data\trading\backups\week_5_baseline_operation_20250706_174834
2025-07-06 17:48:34,747 - __main__ - INFO - 完成周期记录: 第5周 - baseline_operation
2025-07-06 17:48:34,747 - __main__ - INFO - 完成OPRO周期集成: 第5周
2025-07-06 17:48:34,747 - __main__ - INFO - 📊 完成第 5 周数据存储集成
2025-07-06 17:48:34,747 - __main__ - INFO - 🔄 开始第 6 周（ab_testing）: 2025-02-19 到 2025-02-27
2025-07-06 17:48:34,754 - __main__ - INFO - 开始周期记录: week_6_ab_testing_20250706_174834
2025-07-06 17:48:34,754 - __main__ - INFO - 开始OPRO周期集成: week_6_ab_testing_20250706_174834
2025-07-06 17:48:34,754 - __main__ - INFO - 📊 开始第 6 周数据存储集成: week_6_ab_testing_20250706_174834
2025-07-06 17:48:34,754 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-19
2025-07-06 17:48:34,755 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第2/7天
2025-07-06 17:48:34,755 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-20
2025-07-06 17:48:34,755 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第3/7天
2025-07-06 17:48:34,755 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-21
2025-07-06 17:48:34,755 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第4/7天
2025-07-06 17:48:34,755 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-24
2025-07-06 17:48:34,756 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第7/7天
2025-07-06 17:48:34,756 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,756 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,756 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,756 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,757 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,757 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,757 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,757 - __main__ - INFO - Shapley值计算完成，耗时 0.000s
2025-07-06 17:48:34,758 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,758 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,758 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,758 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,758 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-02-18
2025-07-06 17:48:34,758 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,759 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,759 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,759 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-25
2025-07-06 17:48:34,759 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-26
2025-07-06 17:48:34,759 - __main__ - INFO - 🆕 开始第8周 - A/B测试阶段: 2025-02-26
2025-07-06 17:48:34,759 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第1/7天
2025-07-06 17:48:34,760 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-27
2025-07-06 17:48:34,760 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第2/7天
2025-07-06 17:48:34,764 - __main__ - INFO - 记录周期性能指标: 第6周 - optimized
2025-07-06 17:48:34,764 - __main__ - INFO - 📊 记录第 6 周性能指标
2025-07-06 17:48:34,764 - __main__ - INFO - ✅ 第 6 周完成: baseline_complete
2025-07-06 17:48:34,777 - __main__ - INFO - 创建周期备份: data\trading\backups\week_6_ab_testing_20250706_174834
2025-07-06 17:48:34,778 - __main__ - INFO - 完成周期记录: 第6周 - ab_testing
2025-07-06 17:48:34,778 - __main__ - INFO - 完成OPRO周期集成: 第6周
2025-07-06 17:48:34,778 - __main__ - INFO - 📊 完成第 6 周数据存储集成
2025-07-06 17:48:34,778 - __main__ - INFO - 🔄 开始第 7 周（baseline_operation）: 2025-02-28 到 2025-03-10
2025-07-06 17:48:34,781 - __main__ - INFO - 开始周期记录: week_7_baseline_operation_20250706_174834
2025-07-06 17:48:34,782 - __main__ - INFO - 开始OPRO周期集成: week_7_baseline_operation_20250706_174834
2025-07-06 17:48:34,782 - __main__ - INFO - 📊 开始第 7 周数据存储集成: week_7_baseline_operation_20250706_174834
2025-07-06 17:48:34,782 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-28
2025-07-06 17:48:34,782 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第3/7天
2025-07-06 17:48:34,782 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-03
2025-07-06 17:48:34,783 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第6/7天
2025-07-06 17:48:34,783 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-04
2025-07-06 17:48:34,783 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第7/7天
2025-07-06 17:48:34,783 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:48:34,783 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:48:34,783 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:48:34,784 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:48:34,784 - __main__ - INFO - ✅ 智能体 NSA 配置更新成功: optimized
2025-07-06 17:48:34,784 - __main__ - INFO - 🔄 智能体配置更新完成: 成功1, 失败0
2025-07-06 17:48:34,784 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:48:34,784 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_174834
2025-07-06 17:48:34,785 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_174834 - 0 条记录
2025-07-06 17:48:34,785 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_174834
2025-07-06 17:48:34,786 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:48:34,786 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-05
2025-07-06 17:48:34,786 - __main__ - INFO - 🆕 开始第9周 - 基线运行阶段: 2025-03-05
2025-07-06 17:48:34,786 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第1/7天
2025-07-06 17:48:34,786 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-06
2025-07-06 17:48:34,787 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第2/7天
2025-07-06 17:48:34,787 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-07
2025-07-06 17:48:34,787 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第3/7天
2025-07-06 17:48:34,787 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-10
2025-07-06 17:48:34,787 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第6/7天
2025-07-06 17:48:34,792 - __main__ - INFO - 记录周期性能指标: 第7周 - baseline
2025-07-06 17:48:34,793 - __main__ - INFO - 📊 记录第 7 周性能指标
2025-07-06 17:48:34,793 - __main__ - INFO - ✅ 第 7 周完成: ab_testing_complete
2025-07-06 17:48:34,793 - __main__ - INFO - 🔬 第 7 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:48:34,794 - __main__ - WARNING - ⚠️  第 7 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:48:34,794 - __main__ - WARNING - 第7周没有A/B测试对比数据
2025-07-06 17:48:34,794 - __main__ - WARNING - ⚠️ 存储系统中也没有第 7 周的获胜实验数据
2025-07-06 17:48:34,800 - __main__ - INFO - 创建周期备份: data\trading\backups\week_7_baseline_operation_20250706_174834
2025-07-06 17:48:34,801 - __main__ - INFO - 完成周期记录: 第7周 - baseline_operation
2025-07-06 17:48:34,801 - __main__ - INFO - 完成OPRO周期集成: 第7周
2025-07-06 17:48:34,801 - __main__ - INFO - 📊 完成第 7 周数据存储集成
2025-07-06 17:48:34,801 - __main__ - INFO - 🔄 开始第 8 周（ab_testing）: 2025-03-11 到 2025-03-19
2025-07-06 17:48:34,804 - __main__ - INFO - 开始周期记录: week_8_ab_testing_20250706_174834
2025-07-06 17:48:34,804 - __main__ - INFO - 开始OPRO周期集成: week_8_ab_testing_20250706_174834
2025-07-06 17:48:34,804 - __main__ - INFO - 📊 开始第 8 周数据存储集成: week_8_ab_testing_20250706_174834
2025-07-06 17:48:34,804 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-11
2025-07-06 17:48:34,805 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第7/7天
2025-07-06 17:48:34,805 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,805 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,805 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,805 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,805 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,805 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,806 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,806 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-06 17:48:34,806 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,807 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,807 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,807 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,807 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-05
2025-07-06 17:48:34,807 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,807 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,807 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,807 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-12
2025-07-06 17:48:34,808 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-13
2025-07-06 17:48:34,808 - __main__ - INFO - 🆕 开始第10周 - A/B测试阶段: 2025-03-13
2025-07-06 17:48:34,808 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第1/7天
2025-07-06 17:48:34,808 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-14
2025-07-06 17:48:34,808 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第2/7天
2025-07-06 17:48:34,808 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-17
2025-07-06 17:48:34,808 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第5/7天
2025-07-06 17:48:34,808 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-18
2025-07-06 17:48:34,808 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第6/7天
2025-07-06 17:48:34,808 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-19
2025-07-06 17:48:34,808 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第7/7天
2025-07-06 17:48:34,809 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:48:34,809 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:48:34,809 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:48:34,809 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:48:34,809 - __main__ - INFO - ✅ 智能体 NSA 配置更新成功: optimized
2025-07-06 17:48:34,809 - __main__ - INFO - 🔄 智能体配置更新完成: 成功1, 失败0
2025-07-06 17:48:34,809 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:48:34,809 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_174834
2025-07-06 17:48:34,810 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_174834 - 0 条记录
2025-07-06 17:48:34,810 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_174834
2025-07-06 17:48:34,810 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:48:34,814 - __main__ - INFO - 记录周期性能指标: 第8周 - optimized
2025-07-06 17:48:34,814 - __main__ - INFO - 📊 记录第 8 周性能指标
2025-07-06 17:48:34,814 - __main__ - INFO - ✅ 第 8 周完成: ab_testing_complete
2025-07-06 17:48:34,814 - __main__ - INFO - 🔬 第 8 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:48:34,814 - __main__ - WARNING - ⚠️  第 8 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:48:34,814 - __main__ - WARNING - 第8周没有A/B测试对比数据
2025-07-06 17:48:34,814 - __main__ - WARNING - ⚠️ 存储系统中也没有第 8 周的获胜实验数据
2025-07-06 17:48:34,822 - __main__ - INFO - 创建周期备份: data\trading\backups\week_8_ab_testing_20250706_174834
2025-07-06 17:48:34,822 - __main__ - INFO - 完成周期记录: 第8周 - ab_testing
2025-07-06 17:48:34,822 - __main__ - INFO - 完成OPRO周期集成: 第8周
2025-07-06 17:48:34,822 - __main__ - INFO - 📊 完成第 8 周数据存储集成
2025-07-06 17:48:34,822 - __main__ - INFO - 🔄 开始第 9 周（baseline_operation）: 2025-03-20 到 2025-03-28
2025-07-06 17:48:34,828 - __main__ - INFO - 开始周期记录: week_9_baseline_operation_20250706_174834
2025-07-06 17:48:34,829 - __main__ - INFO - 开始OPRO周期集成: week_9_baseline_operation_20250706_174834
2025-07-06 17:48:34,829 - __main__ - INFO - 📊 开始第 9 周数据存储集成: week_9_baseline_operation_20250706_174834
2025-07-06 17:48:34,829 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-20
2025-07-06 17:48:34,829 - __main__ - INFO - 🆕 开始第11周 - 基线运行阶段: 2025-03-20
2025-07-06 17:48:34,830 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第1/7天
2025-07-06 17:48:34,830 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-21
2025-07-06 17:48:34,830 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第2/7天
2025-07-06 17:48:34,830 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-24
2025-07-06 17:48:34,830 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第5/7天
2025-07-06 17:48:34,830 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-25
2025-07-06 17:48:34,830 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第6/7天
2025-07-06 17:48:34,830 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-26
2025-07-06 17:48:34,830 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第7/7天
2025-07-06 17:48:34,830 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:48:34,830 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['TRA', 'NSA', 'FSA']
2025-07-06 17:48:34,830 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:48:34,831 - __main__ - INFO - 从缓存数据构建了 8 个联盟值
2025-07-06 17:48:34,831 - __main__ - INFO - 开始计算 3 个智能体的Shapley值
2025-07-06 17:48:34,831 - __main__ - INFO - 已提供 8 个联盟的特征函数值
2025-07-06 17:48:34,831 - __main__ - INFO - 联盟值补全完成: 已提供 8 个，补全 0 个
2025-07-06 17:48:34,831 - __main__ - INFO - Shapley值计算完成，耗时 0.000s
2025-07-06 17:48:34,832 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:48:34,832 - __main__ - INFO - 🎯 识别到1个低表现智能体: ['NSA']
2025-07-06 17:48:34,832 - __main__ - INFO - 🔧 开始优化1个低表现智能体: ['NSA']
2025-07-06 17:48:34,832 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NSA']
2025-07-06 17:48:34,832 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-20
2025-07-06 17:48:34,832 - __main__ - ERROR - 每周优化循环失败: OPRO优化器未初始化
2025-07-06 17:48:34,832 - __main__ - WARNING - ⚠️ OPRP优化部分失败: OPRO优化器未初始化
2025-07-06 17:48:34,832 - __main__ - INFO - 已存储1个智能体的优化提示词
2025-07-06 17:48:34,832 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-27
2025-07-06 17:48:34,834 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-28
2025-07-06 17:48:34,834 - __main__ - INFO - 🆕 开始第12周 - A/B测试阶段: 2025-03-28
2025-07-06 17:48:34,834 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第12周，第1/7天
2025-07-06 17:48:34,837 - __main__ - INFO - 记录周期性能指标: 第9周 - baseline
2025-07-06 17:48:34,838 - __main__ - INFO - 📊 记录第 9 周性能指标
2025-07-06 17:48:34,838 - __main__ - INFO - ✅ 第 9 周完成: baseline_complete
2025-07-06 17:48:34,845 - __main__ - INFO - 创建周期备份: data\trading\backups\week_9_baseline_operation_20250706_174834
2025-07-06 17:48:34,845 - __main__ - INFO - 完成周期记录: 第9周 - baseline_operation
2025-07-06 17:48:34,846 - __main__ - INFO - 完成OPRO周期集成: 第9周
2025-07-06 17:48:34,846 - __main__ - INFO - 📊 完成第 9 周数据存储集成
2025-07-06 17:48:34,846 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 17:48:34,846 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 17:48:34,846 - __main__ - INFO -   - 总周期数: 9
2025-07-06 17:48:34,846 - __main__ - INFO -   - 成功周期: 8
2025-07-06 17:48:34,846 - __main__ - INFO -   - 失败周期: 1
2025-07-06 17:48:34,846 - __main__ - INFO -   - 成功率: 88.89%
2025-07-06 17:48:34,847 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 17:48:34,847 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,847 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 17:48:34,847 - __main__ - INFO - 📊 总周期数: 9
2025-07-06 17:48:34,847 - __main__ - INFO - 📊 总交易天数: 65
2025-07-06 17:48:34,847 - __main__ - INFO - ⏱️  总执行时间: 0.25秒
2025-07-06 17:48:34,847 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,847 - __main__ - INFO - ✅ 增强仪表板模式执行完成
2025-07-06 17:48:34,848 - __main__ - INFO - ====================================================================================================
2025-07-06 17:48:34,848 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 17:48:34,848 - __main__ - INFO - ====================================================================================================
