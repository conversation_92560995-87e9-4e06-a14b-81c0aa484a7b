#!/usr/bin/env python3
"""
测试AgentInteractionLogger修复验证脚本

验证JSON序列化修复和错误处理改进是否有效
"""

import sys
import os
import json
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.agent_interaction_logger import AgentInteractionLogger

def test_corrupted_json_handling():
    """测试损坏JSON文件的处理"""
    print("🧪 测试损坏JSON文件处理...")
    
    # 创建测试目录
    test_dir = Path("test_corrupted_json")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    try:
        # 创建损坏的JSON文件
        corrupted_file = test_dir / "corrupted.json"
        with open(corrupted_file, 'w', encoding='utf-8') as f:
            f.write('{"incomplete": "json"')  # 故意写入不完整的JSON
        
        # 初始化logger
        logger = AgentInteractionLogger(
            enabled=True,
            base_path=str(test_dir),
            use_trading_dates=False
        )
        
        # 尝试加载损坏的文件
        result = logger._load_existing_data(corrupted_file)
        
        print(f"✅ 损坏JSON文件处理成功，返回: {result}")
        print(f"✅ 文件是否被删除: {not corrupted_file.exists()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 损坏JSON文件处理失败: {e}")
        return False
    finally:
        # 清理测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)

def test_enhanced_error_handling():
    """测试增强的错误处理"""
    print("🧪 测试增强错误处理...")
    
    # 创建测试目录
    test_dir = Path("test_error_handling")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    try:
        # 初始化logger
        logger = AgentInteractionLogger(
            enabled=True,
            base_path=str(test_dir),
            use_trading_dates=True
        )
        
        # 测试各种问题数据类型
        test_cases = [
            {
                "name": "正常数据",
                "state_data": {"date": "2025-01-06", "cash": 1000000},
                "previous_outputs": {"agent1": "output1"},
                "should_succeed": True
            },
            {
                "name": "列表类型previous_outputs",
                "state_data": {"date": "2025-01-06", "cash": 1000000},
                "previous_outputs": ["output1", "output2"],
                "should_succeed": True
            },
            {
                "name": "None类型数据",
                "state_data": {"date": "2025-01-06", "cash": 1000000},
                "previous_outputs": None,
                "should_succeed": True
            }
        ]
        
        success_count = 0
        for test_case in test_cases:
            try:
                result = logger.log_agent_input(
                    agent_name="TEST_AGENT",
                    state_data=test_case["state_data"],
                    previous_outputs=test_case["previous_outputs"]
                )
                
                if result and test_case["should_succeed"]:
                    print(f"✅ {test_case['name']}: 成功")
                    success_count += 1
                elif not result and not test_case["should_succeed"]:
                    print(f"✅ {test_case['name']}: 预期失败")
                    success_count += 1
                else:
                    print(f"❌ {test_case['name']}: 意外结果")
                    
            except Exception as e:
                if test_case["should_succeed"]:
                    print(f"❌ {test_case['name']}: 意外异常 - {e}")
                else:
                    print(f"✅ {test_case['name']}: 预期异常")
                    success_count += 1
        
        print(f"✅ 错误处理测试完成: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False
    finally:
        # 清理测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)

def test_multi_day_logging():
    """测试多日日志记录"""
    print("🧪 测试多日日志记录...")
    
    # 创建测试目录
    test_dir = Path("test_multi_day")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    try:
        # 初始化logger
        logger = AgentInteractionLogger(
            enabled=True,
            base_path=str(test_dir),
            use_trading_dates=True
        )
        
        # 模拟多日记录
        dates = ["2025-01-06", "2025-01-07", "2025-01-08"]
        agents = ["NAA", "TAA", "TRA"]
        
        success_count = 0
        total_operations = 0
        
        for date in dates:
            for agent in agents:
                try:
                    # 只测试输入记录，这是最关键的功能
                    input_id = logger.log_agent_input(
                        agent_name=agent,
                        state_data={"date": date, "cash": 1000000},
                        previous_outputs={"previous": f"output_for_{date}"}
                    )

                    if input_id:
                        success_count += 1

                    total_operations += 1

                except Exception as e:
                    print(f"❌ {agent} on {date} 记录失败: {e}")
                    total_operations += 1
        
        print(f"✅ 多日日志记录测试完成: {success_count}/{total_operations} 成功")

        # 检查实际创建的目录结构
        print(f"📁 检查实际目录结构...")
        if test_dir.exists():
            for item in test_dir.rglob("*"):
                if item.is_dir():
                    print(f"  📂 {item.relative_to(test_dir)}")
                elif item.is_file():
                    print(f"  📄 {item.relative_to(test_dir)}")

        # 验证文件结构
        expected_dirs = []
        for date in dates:
            for agent in agents:
                expected_dirs.append(test_dir / date / "original_track" / agent)

        existing_dirs = [d for d in expected_dirs if d.exists()]
        print(f"✅ 目录结构验证: {len(existing_dirs)}/{len(expected_dirs)} 目录存在")

        # 验证文件内容
        file_count = 0
        for existing_dir in existing_dirs:
            inputs_file = existing_dir / "inputs.json"
            if inputs_file.exists():
                file_count += 1

        print(f"✅ 输入文件验证: {file_count}/{len(existing_dirs)} 文件存在")

        return success_count == total_operations
        
    except Exception as e:
        print(f"❌ 多日日志记录测试失败: {e}")
        return False
    finally:
        # 清理测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)

def main():
    """主测试函数"""
    print("🚀 开始AgentInteractionLogger修复验证测试")
    print("=" * 60)
    
    tests = [
        ("损坏JSON文件处理", test_corrupted_json_handling),
        ("增强错误处理", test_enhanced_error_handling),
        ("多日日志记录", test_multi_day_logging)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AgentInteractionLogger修复验证成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
