#!/usr/bin/env python3
"""
代理交互日志记录器 (Agent Interaction Logger)

专门负责记录每个代理的日常交互数据，包括：
1. 输入数据 (inputs.json) - 代理接收到的所有输入
2. 提示词数据 (prompts.json) - 代理使用的当前提示词/指令
3. 输出数据 (outputs.json) - 代理的推理输出和决策

文件结构：
/data/trading/{experiment_date}/{agent_name}/
├── inputs.json     - 所有输入数据
├── prompts.json    - 当前提示词
└── outputs.json    - 所有输出数据

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
import uuid
import pandas as pd
import numpy as np

class CustomJSONEncoder(json.JSONEncoder):
    """
    自定义JSON编码器，处理pandas Timestamp和其他特殊类型
    """
    def default(self, obj):
        if isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, (pd.Series, pd.DataFrame)):
            return obj.to_dict()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'isoformat'):  # datetime objects
            return obj.isoformat()
        return super().default(obj)

def safe_serialize_key(key: Any) -> str:
    """
    安全地序列化字典键

    参数:
        key: 要序列化的键

    返回:
        字符串格式的键
    """
    if isinstance(key, pd.Timestamp):
        return key.isoformat()
    elif hasattr(key, 'isoformat'):  # datetime objects
        return key.isoformat()
    elif isinstance(key, (np.integer, np.floating)):
        return str(key)
    else:
        return str(key)

def safe_serialize_data(data: Any) -> Any:
    """
    安全地序列化数据，处理各种特殊类型

    参数:
        data: 要序列化的数据

    返回:
        序列化安全的数据
    """
    if isinstance(data, dict):
        # 安全序列化字典的键和值
        return {safe_serialize_key(key): safe_serialize_data(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [safe_serialize_data(item) for item in data]
    elif isinstance(data, pd.Timestamp):
        return data.isoformat()
    elif isinstance(data, (pd.Series, pd.DataFrame)):
        # 对于pandas对象，也需要处理索引
        dict_data = data.to_dict()
        return safe_serialize_data(dict_data)
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif hasattr(data, 'isoformat'):  # datetime objects
        return data.isoformat()
    else:
        return data

@dataclass
class AgentInputRecord:
    """代理输入记录数据结构"""
    timestamp: str
    input_id: str
    state_data: Dict[str, Any]
    market_data: Dict[str, Any]
    previous_outputs: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class AgentPromptRecord:
    """代理提示词记录数据结构"""
    timestamp: str
    prompt_id: str
    prompt_template: str
    full_prompt: str
    prompt_version: str
    source: str  # "default", "opro", "manual"
    metadata: Dict[str, Any]

@dataclass
class AgentOutputRecord:
    """代理输出记录数据结构"""
    timestamp: str
    output_id: str
    input_id: str  # 关联的输入ID
    prompt_id: str  # 关联的提示词ID
    raw_response: Any
    parsed_output: Dict[str, Any]
    processing_time: float
    llm_used: bool
    confidence: Optional[float]
    reasoning: Optional[str]
    metadata: Dict[str, Any]

class AgentInteractionLogger:
    """
    代理交互日志记录器
    
    负责记录每个代理的输入、提示词和输出数据到结构化文件中
    """
    
    def __init__(self,
                 base_path: str = "data/trading",
                 enabled: bool = True,
                 logger: Optional[logging.Logger] = None,
                 use_trading_dates: bool = True,
                 experiment_track: Optional[str] = None):
        """
        初始化代理交互日志记录器

        参数:
            base_path: 基础存储路径
            enabled: 是否启用日志记录
            logger: 日志记录器
            use_trading_dates: 是否使用交易日期进行分类（而不是固定实验日期）
            experiment_track: 实验轨道标识（如"original_track"或"optimized_track"）
        """
        self.base_path = base_path
        self.enabled = enabled
        self.logger = logger or self._create_default_logger()
        self.use_trading_dates = use_trading_dates
        self.experiment_track = experiment_track  # 新增：实验轨道支持

        # 线程锁，确保并发安全
        self._lock = threading.RLock()

        # 当前实验日期（作为默认值）
        self.current_experiment_date = datetime.now().strftime("%Y-%m-%d")

        # A/B测试和双轨实验支持
        self.ab_test_active = False
        self.current_ab_test_id = None
        self.dual_track_experiment_active = False
        self.current_dual_track_experiment_id = None

        # 内存缓存，用于批量写入
        self._input_cache = {}
        self._prompt_cache = {}
        self._output_cache = {}

        # 确保基础目录存在
        if self.enabled:
            self._ensure_base_directories()

        self.logger.info(f"代理交互日志记录器初始化完成 (启用: {self.enabled}, 使用交易日期: {self.use_trading_dates})")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.AgentInteractionLogger")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_base_directories(self):
        """确保基础目录结构存在"""
        try:
            base_dir = Path(self.base_path)
            base_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建当前实验日期目录
            experiment_dir = base_dir / self.current_experiment_date
            experiment_dir.mkdir(exist_ok=True)
            
            self.logger.debug(f"确保目录结构存在: {experiment_dir}")
            
        except Exception as e:
            self.logger.error(f"创建基础目录失败: {e}")
    
    def _get_agent_directory(self, agent_name: str, trading_date: Optional[str] = None,
                            track_override: Optional[str] = None) -> Path:
        """
        获取代理的专用目录路径

        参数:
            agent_name: 代理名称
            trading_date: 交易日期，如果为None则使用当前实验日期
            track_override: 轨道覆盖标识，用于临时指定轨道

        返回:
            代理目录路径
        """
        # 确定使用的日期
        if self.use_trading_dates and trading_date:
            date_to_use = trading_date
        else:
            date_to_use = self.current_experiment_date

        # 构建基础路径
        base_path = Path(self.base_path) / date_to_use

        # 如果有轨道信息，添加轨道层级
        track_to_use = track_override or self.experiment_track
        if track_to_use:
            base_path = base_path / track_to_use

        agent_dir = base_path / agent_name
        agent_dir.mkdir(parents=True, exist_ok=True)
        return agent_dir
    
    def _load_existing_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        加载现有的JSON数据文件

        参数:
            file_path: 文件路径

        返回:
            现有数据列表
        """
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 确保返回的是列表
                    if isinstance(data, list):
                        return data
                    else:
                        self.logger.warning(f"文件 {file_path} 包含非列表数据，转换为列表")
                        return [data] if data is not None else []
            except (json.JSONDecodeError, IOError) as e:
                self.logger.warning(f"加载现有数据失败 {file_path}: {e}")
                # 尝试删除损坏的文件
                try:
                    file_path.unlink()
                    self.logger.info(f"已删除损坏的文件: {file_path}")
                except Exception as delete_error:
                    self.logger.warning(f"无法删除损坏的文件 {file_path}: {delete_error}")
                return []
        return []
    
    def _save_data_to_file(self, file_path: Path, data: List[Dict[str, Any]]):
        """
        保存数据到JSON文件

        参数:
            file_path: 文件路径
            data: 要保存的数据
        """
        try:
            # 使用安全序列化处理数据
            safe_data = safe_serialize_data(data)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(safe_data, f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            self.logger.error(f"保存数据到文件失败 {file_path}: {e}")
            # 尝试使用更安全的方式保存
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, indent=2, ensure_ascii=False)
                self.logger.warning(f"已保存空数据到 {file_path} 以避免数据丢失")
            except Exception as e2:
                self.logger.error(f"保存空数据也失败 {file_path}: {e2}")

    def _extract_trading_date(self, state_data: Dict[str, Any]) -> Optional[str]:
        """
        从状态数据中提取交易日期

        参数:
            state_data: 状态数据字典

        返回:
            交易日期字符串 (YYYY-MM-DD格式) 或 None
        """
        # 尝试从多个可能的字段中提取日期
        date_fields = [
            "current_date",
            "date",
            "trading_date",
            "analysis_date"
        ]

        for field in date_fields:
            if field in state_data:
                date_value = state_data[field]
                if isinstance(date_value, str):
                    # 验证日期格式
                    try:
                        datetime.strptime(date_value, "%Y-%m-%d")
                        return date_value
                    except ValueError:
                        continue

        # 如果从analysis_period中提取
        if "analysis_period" in state_data:
            period = state_data["analysis_period"]
            if isinstance(period, dict):
                # 使用结束日期作为交易日期
                if "end_date" in period:
                    end_date = period["end_date"]
                    if isinstance(end_date, str):
                        try:
                            datetime.strptime(end_date, "%Y-%m-%d")
                            return end_date
                        except ValueError:
                            pass

                # 或者使用开始日期
                if "start_date" in period:
                    start_date = period["start_date"]
                    if isinstance(start_date, str):
                        try:
                            datetime.strptime(start_date, "%Y-%m-%d")
                            return start_date
                        except ValueError:
                            pass

        # 如果都没找到，返回None（将使用默认实验日期）
        return None

    def set_experiment_track(self, track_name: str):
        """
        设置实验轨道

        参数:
            track_name: 轨道名称（如"original_track"或"optimized_track"）
        """
        with self._lock:
            self.experiment_track = track_name
            self.logger.info(f"设置实验轨道: {track_name}")

    def start_ab_test(self, ab_test_id: str, track_name: str):
        """
        启动A/B测试模式

        参数:
            ab_test_id: A/B测试ID
            track_name: 当前轨道名称
        """
        with self._lock:
            self.ab_test_active = True
            self.current_ab_test_id = ab_test_id
            self.experiment_track = track_name
            self.logger.info(f"启动A/B测试: {ab_test_id}, 轨道: {track_name}")

    def stop_ab_test(self):
        """停止A/B测试模式"""
        with self._lock:
            self.ab_test_active = False
            self.current_ab_test_id = None
            self.experiment_track = None
            self.logger.info("停止A/B测试模式")

    def start_dual_track_experiment(self, experiment_id: str, track_name: str):
        """
        启动双轨实验模式

        参数:
            experiment_id: 双轨实验ID
            track_name: 当前轨道名称
        """
        with self._lock:
            self.dual_track_experiment_active = True
            self.current_dual_track_experiment_id = experiment_id
            self.experiment_track = track_name
            self.logger.info(f"启动双轨实验: {experiment_id}, 轨道: {track_name}")

    def stop_dual_track_experiment(self):
        """停止双轨实验模式"""
        with self._lock:
            self.dual_track_experiment_active = False
            self.current_dual_track_experiment_id = None
            self.experiment_track = None
            self.logger.info("停止双轨实验模式")

    def get_current_track_info(self) -> Dict[str, Any]:
        """
        获取当前轨道信息

        返回:
            轨道信息字典
        """
        return {
            "experiment_track": self.experiment_track,
            "ab_test_active": self.ab_test_active,
            "current_ab_test_id": self.current_ab_test_id,
            "dual_track_experiment_active": self.dual_track_experiment_active,
            "current_dual_track_experiment_id": self.current_dual_track_experiment_id,
            "current_experiment_date": self.current_experiment_date
        }
    
    def log_agent_input(self,
                       agent_name: str,
                       state_data: Dict[str, Any],
                       market_data: Optional[Dict[str, Any]] = None,
                       previous_outputs: Optional[Dict[str, Any]] = None,
                       metadata: Optional[Dict[str, Any]] = None,
                       trading_date: Optional[str] = None,
                       track_name: Optional[str] = None) -> str:
        """
        记录代理输入数据

        参数:
            agent_name: 代理名称
            state_data: 状态数据
            market_data: 市场数据
            previous_outputs: 前序输出
            metadata: 元数据
            trading_date: 交易日期（如果为None则从state_data中提取）
            track_name: 实验轨道名称（如果为None则使用当前设置的轨道）

        返回:
            输入记录ID
        """
        if not self.enabled:
            return ""

        try:
            with self._lock:
                # 提取交易日期
                if not trading_date and self.use_trading_dates:
                    trading_date = self._extract_trading_date(state_data)

                input_id = f"input_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

                # 安全处理输入数据，确保数据结构正确
                try:
                    safe_state_data = safe_serialize_data(state_data or {})
                except Exception as e:
                    self.logger.error(f"序列化state_data失败: {e}, 数据类型: {type(state_data)}")
                    safe_state_data = {}

                try:
                    safe_market_data = safe_serialize_data(market_data or {})
                except Exception as e:
                    self.logger.error(f"序列化market_data失败: {e}, 数据类型: {type(market_data)}")
                    safe_market_data = {}

                # 处理previous_outputs，确保它是字典类型
                safe_previous_outputs = {}
                if previous_outputs is not None:
                    try:
                        if isinstance(previous_outputs, dict):
                            safe_previous_outputs = safe_serialize_data(previous_outputs)
                        elif isinstance(previous_outputs, list):
                            # 如果是列表，转换为字典格式
                            safe_previous_outputs = {f"output_{i}": safe_serialize_data(item) for i, item in enumerate(previous_outputs)}
                            self.logger.debug(f"将列表类型的previous_outputs转换为字典: {len(previous_outputs)} 项")
                        else:
                            # 其他类型，包装为字典
                            safe_previous_outputs = {"data": safe_serialize_data(previous_outputs)}
                            self.logger.debug(f"将{type(previous_outputs)}类型的previous_outputs包装为字典")
                    except Exception as e:
                        self.logger.error(f"序列化previous_outputs失败: {e}, 数据类型: {type(previous_outputs)}")
                        safe_previous_outputs = {}

                try:
                    safe_metadata = safe_serialize_data(metadata or {})
                except Exception as e:
                    self.logger.error(f"序列化metadata失败: {e}, 数据类型: {type(metadata)}")
                    safe_metadata = {}

                input_record = AgentInputRecord(
                    timestamp=datetime.now().isoformat(),
                    input_id=input_id,
                    state_data=safe_state_data,
                    market_data=safe_market_data,
                    previous_outputs=safe_previous_outputs,
                    metadata=safe_metadata
                )

                # 获取代理目录（传递交易日期和轨道信息）
                agent_dir = self._get_agent_directory(agent_name, trading_date, track_name)
                inputs_file = agent_dir / "inputs.json"

                # 加载现有数据
                existing_data = self._load_existing_data(inputs_file)

                # 添加新记录
                existing_data.append(asdict(input_record))

                # 保存数据
                self._save_data_to_file(inputs_file, existing_data)

                self.logger.debug(f"记录代理输入: {agent_name} - {input_id}")
                return input_id

        except Exception as e:
            self.logger.error(f"记录代理输入失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return ""
    
    def log_agent_prompt(self,
                        agent_name: str,
                        prompt_template: str,
                        full_prompt: str,
                        prompt_version: str = "1.0.0",
                        source: str = "default",
                        metadata: Optional[Dict[str, Any]] = None,
                        trading_date: Optional[str] = None,
                        track_name: Optional[str] = None) -> str:
        """
        记录代理提示词数据

        参数:
            agent_name: 代理名称
            prompt_template: 提示词模板
            full_prompt: 完整提示词
            prompt_version: 提示词版本
            source: 提示词来源
            metadata: 元数据
            trading_date: 交易日期
            track_name: 实验轨道名称（如果为None则使用当前设置的轨道）

        返回:
            提示词记录ID
        """
        if not self.enabled:
            return ""
        
        try:
            with self._lock:
                # 如果没有提供交易日期，尝试从元数据中提取
                if not trading_date and self.use_trading_dates and metadata:
                    trading_date = metadata.get("trading_date")

                prompt_id = f"prompt_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

                # 安全序列化元数据
                safe_metadata = safe_serialize_data(metadata or {})

                prompt_record = AgentPromptRecord(
                    timestamp=datetime.now().isoformat(),
                    prompt_id=prompt_id,
                    prompt_template=prompt_template,
                    full_prompt=full_prompt,
                    prompt_version=prompt_version,
                    source=source,
                    metadata=safe_metadata
                )

                # 获取代理目录（传递交易日期和轨道信息）
                agent_dir = self._get_agent_directory(agent_name, trading_date, track_name)
                prompts_file = agent_dir / "prompts.json"
                
                # 加载现有数据
                existing_data = self._load_existing_data(prompts_file)
                
                # 添加新记录
                existing_data.append(asdict(prompt_record))
                
                # 保存数据
                self._save_data_to_file(prompts_file, existing_data)
                
                self.logger.debug(f"记录代理提示词: {agent_name} - {prompt_id}")
                return prompt_id
                
        except Exception as e:
            self.logger.error(f"记录代理提示词失败: {e}")
            return ""

    def log_agent_output(self,
                        agent_name: str,
                        input_id: str,
                        prompt_id: str,
                        raw_response: Any,
                        parsed_output: Dict[str, Any],
                        processing_time: float,
                        llm_used: bool = True,
                        confidence: Optional[float] = None,
                        reasoning: Optional[str] = None,
                        metadata: Optional[Dict[str, Any]] = None,
                        trading_date: Optional[str] = None,
                        track_name: Optional[str] = None) -> str:
        """
        记录代理输出数据

        参数:
            agent_name: 代理名称
            input_id: 关联的输入ID
            prompt_id: 关联的提示词ID
            raw_response: 原始响应
            parsed_output: 解析后的输出
            processing_time: 处理时间
            llm_used: 是否使用了LLM
            confidence: 置信度
            reasoning: 推理过程
            metadata: 元数据
            trading_date: 交易日期
            track_name: 实验轨道名称（如果为None则使用当前设置的轨道）

        返回:
            输出记录ID
        """
        if not self.enabled:
            return ""

        try:
            with self._lock:
                output_id = f"output_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

                # 安全序列化所有数据
                safe_raw_response = safe_serialize_data(raw_response)
                safe_parsed_output = safe_serialize_data(parsed_output)
                safe_metadata = safe_serialize_data(metadata or {})

                output_record = AgentOutputRecord(
                    timestamp=datetime.now().isoformat(),
                    output_id=output_id,
                    input_id=input_id,
                    prompt_id=prompt_id,
                    raw_response=safe_raw_response,
                    parsed_output=safe_parsed_output,
                    processing_time=processing_time,
                    llm_used=llm_used,
                    confidence=confidence,
                    reasoning=reasoning,
                    metadata=safe_metadata
                )

                # 获取代理目录（传递交易日期和轨道信息）
                agent_dir = self._get_agent_directory(agent_name, trading_date, track_name)
                outputs_file = agent_dir / "outputs.json"

                # 加载现有数据
                existing_data = self._load_existing_data(outputs_file)

                # 添加新记录
                existing_data.append(asdict(output_record))

                # 保存数据
                self._save_data_to_file(outputs_file, existing_data)

                self.logger.debug(f"记录代理输出: {agent_name} - {output_id}")
                return output_id

        except Exception as e:
            self.logger.error(f"记录代理输出失败: {e}")
            return ""

    def set_experiment_date(self, date_str: str):
        """
        设置实验日期

        参数:
            date_str: 日期字符串 (YYYY-MM-DD格式)
        """
        try:
            # 验证日期格式
            datetime.strptime(date_str, "%Y-%m-%d")
            self.current_experiment_date = date_str

            # 确保新日期的目录存在
            if self.enabled:
                self._ensure_base_directories()

            self.logger.info(f"设置实验日期: {date_str}")

        except ValueError as e:
            self.logger.error(f"无效的日期格式: {date_str}, 应为YYYY-MM-DD格式")

    def get_agent_log_summary(self, agent_name: str) -> Dict[str, Any]:
        """
        获取代理日志摘要

        参数:
            agent_name: 代理名称

        返回:
            日志摘要信息
        """
        if not self.enabled:
            return {"enabled": False}

        try:
            agent_dir = self._get_agent_directory(agent_name)

            # 统计各类文件的记录数量
            inputs_file = agent_dir / "inputs.json"
            prompts_file = agent_dir / "prompts.json"
            outputs_file = agent_dir / "outputs.json"

            inputs_count = len(self._load_existing_data(inputs_file))
            prompts_count = len(self._load_existing_data(prompts_file))
            outputs_count = len(self._load_existing_data(outputs_file))

            return {
                "enabled": True,
                "agent_name": agent_name,
                "experiment_date": self.current_experiment_date,
                "log_directory": str(agent_dir),
                "inputs_count": inputs_count,
                "prompts_count": prompts_count,
                "outputs_count": outputs_count,
                "files_exist": {
                    "inputs.json": inputs_file.exists(),
                    "prompts.json": prompts_file.exists(),
                    "outputs.json": outputs_file.exists()
                }
            }

        except Exception as e:
            self.logger.error(f"获取代理日志摘要失败: {e}")
            return {"enabled": True, "error": str(e)}

    def get_all_agents_summary(self) -> Dict[str, Any]:
        """
        获取所有代理的日志摘要

        返回:
            所有代理的日志摘要
        """
        if not self.enabled:
            return {"enabled": False}

        try:
            experiment_dir = Path(self.base_path) / self.current_experiment_date

            if not experiment_dir.exists():
                return {
                    "enabled": True,
                    "experiment_date": self.current_experiment_date,
                    "agents": {},
                    "total_agents": 0
                }

            agents_summary = {}

            # 遍历所有代理目录
            for agent_dir in experiment_dir.iterdir():
                if agent_dir.is_dir():
                    agent_name = agent_dir.name
                    agents_summary[agent_name] = self.get_agent_log_summary(agent_name)

            return {
                "enabled": True,
                "experiment_date": self.current_experiment_date,
                "base_directory": str(experiment_dir),
                "agents": agents_summary,
                "total_agents": len(agents_summary)
            }

        except Exception as e:
            self.logger.error(f"获取所有代理日志摘要失败: {e}")
            return {"enabled": True, "error": str(e)}

    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        清理旧的日志文件

        参数:
            days_to_keep: 保留的天数
        """
        if not self.enabled:
            return

        try:
            base_dir = Path(self.base_path)
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            removed_count = 0

            for date_dir in base_dir.iterdir():
                if date_dir.is_dir():
                    try:
                        # 解析目录名为日期
                        dir_date = datetime.strptime(date_dir.name, "%Y-%m-%d")

                        if dir_date < cutoff_date:
                            # 删除整个日期目录
                            import shutil
                            shutil.rmtree(date_dir)
                            removed_count += 1
                            self.logger.info(f"删除旧日志目录: {date_dir}")

                    except ValueError:
                        # 跳过不符合日期格式的目录
                        continue

            self.logger.info(f"清理完成，删除了 {removed_count} 个旧日志目录")

        except Exception as e:
            self.logger.error(f"清理旧日志失败: {e}")

    def export_agent_logs(self,
                         agent_name: str,
                         export_format: str = "json") -> Dict[str, Any]:
        """
        导出代理日志数据

        参数:
            agent_name: 代理名称
            export_format: 导出格式 ("json", "csv")

        返回:
            导出结果
        """
        if not self.enabled:
            return {"enabled": False}

        try:
            agent_dir = self._get_agent_directory(agent_name)

            # 加载所有数据
            inputs_data = self._load_existing_data(agent_dir / "inputs.json")
            prompts_data = self._load_existing_data(agent_dir / "prompts.json")
            outputs_data = self._load_existing_data(agent_dir / "outputs.json")

            export_data = {
                "agent_name": agent_name,
                "experiment_date": self.current_experiment_date,
                "export_timestamp": datetime.now().isoformat(),
                "inputs": inputs_data,
                "prompts": prompts_data,
                "outputs": outputs_data
            }

            # 创建导出文件
            export_filename = f"{agent_name}_logs_{self.current_experiment_date}.{export_format}"
            export_path = agent_dir / export_filename

            if export_format == "json":
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
            elif export_format == "csv":
                # 简化的CSV导出（仅输出数据）
                import pandas as pd
                df = pd.DataFrame(outputs_data)
                df.to_csv(export_path, index=False, encoding='utf-8')

            return {
                "success": True,
                "export_path": str(export_path),
                "export_format": export_format,
                "records_exported": {
                    "inputs": len(inputs_data),
                    "prompts": len(prompts_data),
                    "outputs": len(outputs_data)
                }
            }

        except Exception as e:
            self.logger.error(f"导出代理日志失败: {e}")
            return {"success": False, "error": str(e)}
