2025-07-06 20:10:56,091 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,091 - __main__ - INFO - OPRO系统启动
2025-07-06 20:10:56,091 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,091 - __main__ - INFO - 运行模式: integrated
2025-07-06 20:10:56,091 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 20:10:56,091 - __main__ - INFO - OPRO启用: True
2025-07-06 20:10:56,092 - __main__ - INFO - 数据存储启用: True
2025-07-06 20:10:56,092 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 20:10:56,099 - __main__ - INFO - 初始化系统...
2025-07-06 20:10:56,099 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 20:10:56,099 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 20:10:56,100 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 20:10:56,100 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 20:10:56,100 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 20:10:56,104 - __main__ - INFO - 数据库初始化完成
2025-07-06 20:10:56,105 - __main__ - INFO - 自动备份线程已启动
2025-07-06 20:10:56,105 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:10:56,105 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 20:10:56,105 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 20:10:56,116 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:10:56,116 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:10:56,116 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 20:10:56,121 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 20:10:56,121 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 20:10:56,121 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 20:10:56,122 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 20:10:56,122 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 20:10:56,122 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 20:10:56,123 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 20:10:56,123 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 20:10:56,123 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 20:10:56,136 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:10:56,136 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 20:10:56,136 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 20:10:56,136 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:10:56,136 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 20:10:56,137 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 20:10:56,137 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 20:10:56,137 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 20:10:56,137 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 20:10:56,139 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 20:10:56,140 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 20:10:56,140 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:10:56,151 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 20:10:56,151 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 20:10:56,152 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 20:10:56,152 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:10:56,154 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:10:56,289 - __main__ - ERROR - 创建数据备份失败: [('data/prompts\\BeOA\\opt_BeOA_20250706_154325_6f0c7b06.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_154325_6f0c7b06.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_154734_cfdf758d.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_154734_cfdf758d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_165517_4e53fde0.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_165517_4e53fde0.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171039_cae4f742.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171039_cae4f742.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171226_ae19aa85.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171226_ae19aa85.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171424_86e3fcca.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171424_86e3fcca.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175026_e24faac9.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175026_e24faac9.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175156_6f814b6e.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175156_6f814b6e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175331_1e5c0a74.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175331_1e5c0a74.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_193539_08d7feeb.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_193539_08d7feeb.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_193929_fac736cf.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_193929_fac736cf.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250705_225400_78bcfa18.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250705_225400_78bcfa18.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_130702_d48c08b5.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_130702_d48c08b5.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_154253_ee465f98.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_154253_ee465f98.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_154759_05453ba3.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_154759_05453ba3.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171004_eee06c6b.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171004_eee06c6b.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171159_4e21b097.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171159_4e21b097.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171351_0e5f9c9b.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171351_0e5f9c9b.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175000_e32da3af.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175000_e32da3af.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175131_c19149a7.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175131_c19149a7.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175221_63a807d5.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175221_63a807d5.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175358_e995e078.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175358_e995e078.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_193758_0496538d.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_193758_0496538d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_193957_8d090b30.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_193957_8d090b30.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_223454_7de04663.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_223454_7de04663.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_230016_dafe3e5f.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_230016_dafe3e5f.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_231020_1631d876.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_231020_1631d876.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_232223_a7168861.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_232223_a7168861.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_232528_828cf081.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_232528_828cf081.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NAA\\opt_NAA_20250705_223822_939ec0a9.json', 'data/backups\\backup_20250706_201056\\prompts\\NAA\\opt_NAA_20250705_223822_939ec0a9.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NAA\\opt_NAA_20250705_232504_e9292cd2.json', 'data/backups\\backup_20250706_201056\\prompts\\NAA\\opt_NAA_20250705_232504_e9292cd2.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NAA\\opt_NAA_20250706_130531_af112964.json', 'data/backups\\backup_20250706_201056\\prompts\\NAA\\opt_NAA_20250706_130531_af112964.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NOA\\opt_NOA_20250706_130813_88041910.json', 'data/backups\\backup_20250706_201056\\prompts\\NOA\\opt_NOA_20250706_130813_88041910.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_232158_2ffe76f6.json', 'data/backups\\backup_20250706_201056\\prompts\\TAA\\opt_TAA_20250705_232158_2ffe76f6.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250706_165641_97b460aa.json', 'data/backups\\backup_20250706_201056\\prompts\\TAA\\opt_TAA_20250706_165641_97b460aa.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_222038_1f75fab2.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_222038_1f75fab2.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_223845_e8de2143.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_223845_e8de2143.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_225721_e736628c.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_225721_e736628c.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_230716_3883ac3e.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_230716_3883ac3e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_231339_001064b6.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_231339_001064b6.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232058_526bab8e.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232058_526bab8e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232347_7dc8f556.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232347_7dc8f556.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232550_8b5d2c96.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232550_8b5d2c96.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250706_130845_ce91b106.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250706_130845_ce91b106.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250706_151414_4ade6729.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250706_151414_4ade6729.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 20:10:56,289 - contribution_assessment.assessor.ContributionAssessor - ERROR - 创建数据备份失败: [('data/prompts\\BeOA\\opt_BeOA_20250706_154226_6027c255.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_154226_6027c255.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_154834_3c9f152f.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_154834_3c9f152f.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_165616_c29bc725.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_165616_c29bc725.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171136_7dc3b2aa.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171136_7dc3b2aa.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171326_4c00b5ae.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171326_4c00b5ae.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_171524_6c6e2541.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_171524_6c6e2541.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175112_fc07205d.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175112_fc07205d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175244_169d6216.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175244_169d6216.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_175424_47bb1479.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_175424_47bb1479.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_193640_392e6f29.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_193640_392e6f29.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_193732_f78603f8.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_193732_f78603f8.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BeOA\\opt_BeOA_20250706_194027_52ee51b0.json', 'data/backups\\backup_20250706_201056\\prompts\\BeOA\\opt_BeOA_20250706_194027_52ee51b0.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250705_223518_33b17f82.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250705_223518_33b17f82.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250705_232752_15e1bd3e.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250705_232752_15e1bd3e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_154357_b078fc1a.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_154357_b078fc1a.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_154858_eb590905.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_154858_eb590905.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171108_16f13e43.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171108_16f13e43.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171252_020f121e.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171252_020f121e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_171449_acc14df1.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_171449_acc14df1.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175047_69e86aff.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175047_69e86aff.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_175306_9d99a2ad.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_175306_9d99a2ad.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_193604_ccec6be3.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_193604_ccec6be3.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_193852_bfa26914.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_193852_bfa26914.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\BOA\\opt_BOA_20250706_194053_efc8e5d7.json', 'data/backups\\backup_20250706_201056\\prompts\\BOA\\opt_BOA_20250706_194053_efc8e5d7.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_222303_6e0c1dbc.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_222303_6e0c1dbc.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_225335_f2b67b13.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_225335_f2b67b13.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_230651_f356c28c.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_230651_f356c28c.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_231718_bece3e5d.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_231718_bece3e5d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250705_232406_45001a0c.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250705_232406_45001a0c.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\FAA\\opt_FAA_20250706_130631_9569d71d.json', 'data/backups\\backup_20250706_201056\\prompts\\FAA\\opt_FAA_20250706_130631_9569d71d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NAA\\opt_NAA_20250705_232124_2f31e94b.json', 'data/backups\\backup_20250706_201056\\prompts\\NAA\\opt_NAA_20250705_232124_2f31e94b.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NAA\\opt_NAA_20250705_232625_a1da3cf8.json', 'data/backups\\backup_20250706_201056\\prompts\\NAA\\opt_NAA_20250705_232625_a1da3cf8.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\NOA\\opt_NOA_20250705_232846_7efbc31d.json', 'data/backups\\backup_20250706_201056\\prompts\\NOA\\opt_NOA_20250705_232846_7efbc31d.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_223429_c5c4be04.json', 'data/backups\\backup_20250706_201056\\prompts\\TAA\\opt_TAA_20250705_223429_c5c4be04.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_230959_3ff65287.json', 'data/backups\\backup_20250706_201056\\prompts\\TAA\\opt_TAA_20250705_230959_3ff65287.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_232652_4cfdf4d1.json', 'data/backups\\backup_20250706_201056\\prompts\\TAA\\opt_TAA_20250705_232652_4cfdf4d1.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_221451_793056fb.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_221451_793056fb.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_222400_58e79558.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_222400_58e79558.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_224526_f14eabe7.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_224526_f14eabe7.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_225424_c4e4b48b.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_225424_c4e4b48b.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_230043_4cf17532.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_230043_4cf17532.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_231040_899a9795.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_231040_899a9795.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_231739_5e0a6912.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_231739_5e0a6912.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232252_7189effc.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232252_7189effc.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232437_8e8e8240.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232437_8e8e8240.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232917_6a984190.json', 'data/backups\\backup_20250706_201056\\prompts\\TRA\\opt_TRA_20250705_232917_6a984190.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 20:10:56,311 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:10:56,312 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:10:56,459 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 20:10:56,461 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 20:10:56,593 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 20:10:56,595 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 20:10:56,780 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 20:10:56,780 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 20:10:56,781 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 20:10:56,781 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 20:10:56,781 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 20:10:56,781 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 20:10:56,782 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 20:10:56,783 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 20:10:56,783 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 20:10:56,785 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 20:10:56,785 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 20:10:56,786 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 20:10:56,786 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 20:10:56,786 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 20:10:56,803 - __main__ - INFO - 加载历史数据完成: 15 个实验记录
2025-07-06 20:10:56,803 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 20:10:56,803 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 20:10:56,804 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 20:10:56,804 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 20:10:56,804 - __main__ - INFO - 系统初始化完成
2025-07-06 20:10:56,804 - __main__ - INFO - ================================================================================
2025-07-06 20:10:56,804 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 20:10:56,804 - __main__ - INFO - ================================================================================
2025-07-06 20:10:56,804 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-01-02
2025-07-06 20:10:56,804 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,804 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 20:10:56,805 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,805 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-02
2025-07-06 20:10:56,805 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 20:10:56,805 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 20:10:56,806 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 20:10:56,808 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-02
2025-07-06 20:10:56,809 - __main__ - INFO - 📊 总交易日数: 2
2025-07-06 20:10:56,809 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 20:10:56,810 - __main__ - INFO - 🗓️  最后交易日: 2025-01-02
2025-07-06 20:10:56,810 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 20:10:56,810 - __main__ - INFO - 📊 生成了 0 个7天周期
2025-07-06 20:10:56,811 - __main__ - INFO -    - 基线运行周: 0 个
2025-07-06 20:10:56,811 - __main__ - INFO -    - A/B测试周: 0 个
2025-07-06 20:10:56,811 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 20:10:56,811 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 20:10:56,812 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 20:10:56,812 - __main__ - INFO -   - 总周期数: 0
2025-07-06 20:10:56,812 - __main__ - INFO -   - 成功周期: 0
2025-07-06 20:10:56,812 - __main__ - INFO -   - 失败周期: 0
2025-07-06 20:10:56,812 - __main__ - INFO -   - 成功率: 0.00%
2025-07-06 20:10:56,813 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 20:10:56,813 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,813 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 20:10:56,813 - __main__ - INFO - 📊 总周期数: 0
2025-07-06 20:10:56,814 - __main__ - INFO - 📊 总交易天数: 2
2025-07-06 20:10:56,814 - __main__ - INFO - ⏱️  总执行时间: 0.01秒
2025-07-06 20:10:56,814 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,815 - __main__ - INFO - 开始交易会话: assessment_20250706_201056
2025-07-06 20:10:56,815 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 20:10:56,834 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_201056
2025-07-06 20:10:56,835 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_201056 (系统盈亏: 0.00)
2025-07-06 20:10:56,835 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_201056
2025-07-06 20:10:56,835 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 20:10:56,836 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 20:10:56,836 - __main__ - INFO - ====================================================================================================
2025-07-06 20:10:56,836 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 20:10:56,836 - __main__ - INFO - ====================================================================================================
