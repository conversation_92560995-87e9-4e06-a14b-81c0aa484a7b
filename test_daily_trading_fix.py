#!/usr/bin/env python3
"""
测试每日交易决策修复
验证智能体交互日志记录是否正常工作

创建时间: 2025-07-06
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'test_daily_trading_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)

def test_enhanced_weekly_cycle_with_trading():
    """测试增强的周期性循环是否包含交易决策"""
    logger = setup_logging()
    logger.info("开始测试增强的周期性循环交易决策修复")
    
    try:
        # 1. 导入必要的模块
        from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 2. 创建测试配置
        test_config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-07",
            "stocks": ["AAPL"],
            "simulation_days": 7,
            "enhanced_shapley_oprp": {
                "enabled": True,
                "cycle_length_days": 7,
                "underperforming_threshold": 0.3,
                "max_concurrent_experiments": 2
            }
        }
        
        # 3. 创建智能体交互日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="test_data/trading",
            enabled=True,
            use_trading_dates=True,
            logger=logger
        )
        
        # 设置实验日期
        experiment_date = "2025-01-01"
        interaction_logger.set_experiment_date(experiment_date)
        logger.info(f"智能体交互日志记录器初始化完成，实验日期: {experiment_date}")

        # 4. 创建增强的周期性OPRO管理器
        enhanced_manager = EnhancedWeeklyOPROManager(
            config=test_config,
            base_data_dir="test_data/trading",
            logger=logger,
            interaction_logger=interaction_logger
        )
        logger.info("增强的周期性OPRO管理器初始化完成")
        
        # 5. 测试执行一天的周期
        target_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        daily_results = {}
        
        logger.info("开始执行增强的周期性循环...")
        result = enhanced_manager.run_enhanced_weekly_cycle(
            current_date="2025-01-01",
            target_agents=target_agents,
            daily_results=daily_results
        )

        # 6. 检查结果
        logger.info("检查执行结果...")
        logger.info(f"状态: {result.get('status', 'N/A')}")
        logger.info(f"阶段: {result.get('phase', 'N/A')}")
        logger.info(f"周数: {result.get('week_number', 'N/A')}")
        logger.info(f"周中第几天: {result.get('day_in_week', 'N/A')}")

        # 检查是否执行了交易决策
        if 'daily_trading_result' in result:
            trading_result = result['daily_trading_result']
            logger.info(f"交易决策已执行: {trading_result.get('success', False)}")
            logger.info(f"交易日期: {trading_result.get('date', 'N/A')}")

            if trading_result.get('success'):
                logger.info(f"最佳联盟: {trading_result.get('best_coalition', {}).get('coalition', 'N/A')}")
                logger.info(f"夏普比率: {trading_result.get('best_coalition', {}).get('sharpe_ratio', 'N/A')}")
                logger.info(f"测试联盟数: {trading_result.get('total_coalitions_tested', 'N/A')}")
        else:
            logger.warning("未找到交易决策执行结果")
        
        # 7. 检查智能体交互日志文件
        logger.info("检查智能体交互日志文件...")
        log_base_path = Path("test_data/trading/2025-01-01")

        if log_base_path.exists():
            logger.info(f"日志目录存在: {log_base_path}")

            # 检查各智能体的日志文件
            for agent_name in target_agents:
                agent_dir = log_base_path / agent_name
                if agent_dir.exists():
                    logger.info(f"智能体 {agent_name} 日志目录存在")

                    # 检查具体文件
                    for file_name in ["inputs.json", "prompts.json", "outputs.json"]:
                        file_path = agent_dir / file_name
                        if file_path.exists():
                            logger.info(f"  {file_name} 存在")
                        else:
                            logger.warning(f"  {file_name} 不存在")
                else:
                    logger.warning(f"智能体 {agent_name} 日志目录不存在")
        else:
            logger.warning(f"日志基础目录不存在: {log_base_path}")

        logger.info("测试完成")
        return True

    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_enhanced_weekly_cycle_with_trading()
    if success:
        print("\n测试成功！增强的周期性循环现在包含交易决策执行。")
    else:
        print("\n测试失败！需要进一步调试。")
        sys.exit(1)
