{"week_number": 7, "week_type": "baseline_operation", "completion_timestamp": "2025-07-06T17:48:34.794505", "summary_data": {"week_info": {"week_id": "week_7_baseline_operation_20250706_174834", "week_number": 7, "week_type": "baseline_operation", "start_date": "2025-02-28", "end_date": "2025-03-10", "target_agents": ["TRA", "NSA", "FSA"], "started_at": "2025-07-06T17:48:34.782901"}, "weekly_summary": {"status": "ab_testing_complete", "week_number": 8, "phase": "ab_testing", "ab_test_data": {"week_number": 8, "test_agents": ["NSA"], "original_performance": {}, "optimized_performance": {}, "data_collected": true}, "performance_comparison": {"ab_test_week_8": {"config": {"optimized_agents": ["NSA"]}, "statistical_comparison": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "prompt_selection": {"ab_test_week_8": {"config": {"optimized_agents": ["NSA"]}, "agent_selections": {"NSA": {"selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "statistical_data": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "overall_recommendation": "optimized"}}, "update_result": {"updated_agents": [{"agent_id": "NSA", "selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "experiment_id": "ab_test_week_8", "update_timestamp": "2025-07-06T17:48:34.784493"}], "failed_updates": [], "total_agents": 1}, "weekly_summary": {"week_number": 8, "cycle_type": "ab_testing", "target_agents": ["NSA"], "performance_improvement": 0, "winning_prompts": {}, "winning_experiment_data": {"ab_test_data": {"week_number": 8, "test_agents": ["NSA"], "original_performance": {}, "optimized_performance": {}, "data_collected": true}, "performance_comparison": {"ab_test_week_8": {"config": {"optimized_agents": ["NSA"]}, "statistical_comparison": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "prompt_selection_result": {"ab_test_week_8": {"config": {"optimized_agents": ["NSA"]}, "agent_selections": {"NSA": {"selected_prompt": "optimized", "reason": "优化提示词表现更好 (改进: 0.0500, p值: 0.0100)", "statistical_data": {"recommendation": "optimized", "improvement": 0.05, "p_value": 0.01, "statistical_significance": true, "comparison_complete": true, "winning_track": "optimized"}}}, "overall_recommendation": "optimized"}}, "target_agents": ["NSA"], "week_number": 8}, "timestamp": "2025-07-06T17:48:34.786506"}, "winning_experiment_data": {}, "next_phase": "baseline_operation"}, "integration_completed_at": "2025-07-06T17:48:34.794505"}, "data_statistics": {"total_agent_decisions": 0, "decision_type_distribution": {"buy": 0, "sell": 0, "hold": 0}, "agents_with_data": [], "performance_metrics_recorded": true, "ab_test_tracks": []}, "files_created": ["week_config.json", "performance_data\\performance_metrics_baseline.json"], "status": "completed"}